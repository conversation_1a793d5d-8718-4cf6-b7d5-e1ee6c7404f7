(function(sk, $){

	/*
	 *  Konstruktor pro Záložky
	 */
	 /*
	 	TODO: regulár na komplikovanější hash (#hash1&hash2&tab=test)
	 */


	sk.widgets.Tabs = function(element, options){
		this.$element = element.jquery ? element : $(element);
		this.options = $.extend({
			current: 0,
			item: 'a',
			eventName: 'click touchstart',
			tabHideClass: 'sk-tab-hide',
			menuActiveClass: 'active',
			onActiveTab: function(){},
			history: false
		}, options);
		this.startIndex = this.currentIndex = this.options.current;
		this.currentId = null;
		// tabs menu
		this.$menu = this.$element.find(this.options.item);
		// tabs fragment
		var $tabs = $([]);
		this.$menu.each(function(){
			$tabs = $tabs.add($(this).prop('hash'));
		})
		this.$tabs = $tabs;

		return this;
	};

	// prototype
	var _fn = sk.widgets.Tabs.prototype;

	_fn.init = function(){
		if(!this.$element.length){
			return this;
		}

		var o = this.options;

		this.$element
			.on(o.eventName, o.item, $.proxy(this.handle, this));

		// ur<PERSON>ní která záložka má být defaultně aktivní
		if(o.history)
		{
			this.hash();
		}
		else
		{
			this.set(this.currentIndex);
		}

		return this;
	};

	_fn.refresh = function(id)
	{
		// tabs menu
		this.$menu = this.$element.find(this.options.item);
		// tabs fragment
		var $tabs = $([]);
		this.$menu.each(function(){
			$tabs = $tabs.add($(this).prop('hash'));
		})
		this.$tabs = $tabs;


		this.set( id || this.currentId, false, true )

		return this;
	};

	_fn.hash = function()
	{
		var o = this.options;
		// určování která záložka má být defaultně aktivní
		var tab = '';
		var scroll = '';

		var hash = location.hash.slice(1);
		if(hash)
		{

	        hash = hash.split('tab=');
	       	hash = hash[hash.length - 1];
	       	hash = hash.replace(/^.[^a-z]/g, '');
			this.$tabs.each(function()
			{
		        if($(this).is('#'+hash)){
		        	tab = '#' + $(this).attr('id');

		        	if(location.hash.indexOf('tab=') == -1){

						scroll = '#' + hash;
					}
		        }
		        else if($(this).find('#'+ hash).length){
		        	tab = '#' + $(this).attr('id');
		        	scroll = '#' + hash;
		        }
			})
		}
		// pokud není žádný hash vrátit do startovní záložky
		else if(!/#$/.test(location.href))
		{
			this.currentIndex = this.startIndex;
		}

		this.set(tab || this.currentIndex, scroll);
	};

	_fn.handle = function(e)
	{
		var $target = $(e.currentTarget),
			href = $target.prop('hash').slice(1);

		if(this.options.history)
		{
		    // pokud záložka již není aktivní tak nastavit
		    if(!$target.hasClass(this.options.menuActiveClass))
		    {
		   		location.hash = '#tab=' + href;
		   	}
		}
		else
		{
		   	this.set('#'+href);
		}

		e.preventDefault();
	};

	_fn.set = function(current, scroll, refresh)
	{
		var id = typeof current == 'number' ? this.$menu.eq(current).prop('hash') : current;

		if(this.currentId !== id || refresh)
		{
			this.currentIndex = this.$menu.index( this.$menu.filter(function(){ return $(this).prop('hash') === id }) );
			this.currentId = id;
			this.setTab(id);
			this.setMenu(id);

			scroll && $('html, body').animate({'scrollTop': $(scroll).offset().top}, 500);
		}
	};

	_fn.setTab = function(id){
		this.$tabs
			.filter(id)
			.removeClass(this.options.tabHideClass)
			.end()
			.not(id)
			.addClass(this.options.tabHideClass);

		$(this).trigger('activate');
	};

	_fn.setMenu = function(id){
		this.$menu
			.removeClass(this.options.menuActiveClass)
			.filter(function(){ return $(this).prop('hash') === id })
			.addClass(this.options.menuActiveClass);
	};

})(sk, jQuery);