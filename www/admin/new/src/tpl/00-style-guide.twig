{% extends '@layout/layout.twig' %}
{% set pageTitle = 'Style Guide' %}

{% block content %}

	<main id="main" class="main">
		<div class="row-main">
			<h1>
				Typografie
			</h1>
			<h2>
				Heading h2
			</h2>
			<p>
				Suspendisse <a href="#">link</a> vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.
			</p>
			<p>
				Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
			</p>
			<figure>
				<img src="{{ assets.images }}illust/sample.jpg" width="400" height="250" alt="">
				<figcaption>
					Image description
				</figcaption>
			</figure>
			<h3>
				Heading h3
			</h3>
			<ul>
				<li>
					<strong>Etiam ante sem,</strong> porta a porttitor ut, varius varius metus.
				</li>
				<li>
					<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
				</li>
				<li>
					<strong>Duis gravida</strong> tincidunt enim sed cursus.
				</li>
				<li>
					<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
				</li>
				<li>
					<strong>Duis gravida</strong> tincidunt enim sed cursus.
				</li>
			</ul>
			<ol>
				<li>
					<strong>Etiam ante sem,</strong> porta a porttitor ut, varius varius metus.
				</li>
				<li>
					<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
				</li>
				<li>
					<strong>Duis gravida</strong> tincidunt enim sed cursus.
				</li>
				<li>
					<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
				</li>
				<li>
					<strong>Duis gravida</strong> tincidunt enim sed cursus.
				</li>
			</ol>
			<p>
				Suspendisse <a href="#">link</a> vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.
			</p>
			<p>
				Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
			</p>
			<blockquote>
				<p>
					<strong>Blockquote</strong> – Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolorum quia ipsa corrupti temporibus ratione voluptatibus, voluptatem eos culpa a, numquam suscipit deleniti veniam libero. Dicta soluta sint, officiis enim voluptate.
				</p>
			</blockquote>
			<p>
				Mirum est notare quam littera gothica, quam nunc putamus parum claram, anteposuerit litterarum formas humanitatis per seacula quarta decima et quinta decima. Eodem modo typi, qui nunc nobis videntur parum clari, fiant sollemnes in futurum.<br>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Nam <strong>liber tempor cum soluta nobis</strong> eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
			</p>
			<h4>
				Heading 4 úrovně
			</h4>
			<ol>
				<li>
					Lorem ipsum dolor sit amet.
					<ol>
						<li>
							Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.
						</li>
						<li>
							Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?
						</li>
					</ol>
				</li>
				<li>
					Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod
				</li>
				<li>
					tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse
				</li>
				<li>
					cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
				</li>
			</ol>
			<ul>
				<li>
					Lorem ipsum dolor sit amet.
					<ul>
						<li>
							Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.
						</li>
						<li>
							Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?
						</li>
					</ul>
				</li>
				<li>
					Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod
				</li>
				<li>
					tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse
				</li>
				<li>
					cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
				</li>
			</ul>
			<hr>
			<p>
				Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
			</p>
			<dl>
				<dt>
					Definition List Title
				</dt>
				<dd>
					This is a definition list division.
				</dd>
				<dt>
					Definition List Title
				</dt>
				<dd>
					This is a definition list division.
				</dd>
				<dt>
					Definition List Title
				</dt>
				<dd>
					This is a definition list division.
				</dd>
			</dl>
			<h5>
				Heading 5 úrovně
			</h5>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
			</p>
			<h6>
				Heading 6 úrovně
			</h6>
			<p>
				<cite>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</cite>
			</p>
			<h2>
				Tabular data
			</h2>
			<table>
				<caption>
					Table Caption
				</caption>
				<thead>
					<tr>
						<th>
							Table Heading 1
						</th>
						<th>
							Table Heading 2
						</th>
						<th>
							Table Heading 3
						</th>
						<th>
							Table Heading 4
						</th>
						<th>
							Table Heading 5
						</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>
							Table Cell 1
						</td>
						<td>
							Table Cell 2
						</td>
						<td>
							Table Cell 3
						</td>
						<td>
							Table Cell 4
						</td>
						<td>
							Table Cell 5
						</td>
					</tr>
					<tr>
						<td>
							Table Cell 1
						</td>
						<td>
							Table Cell 2
						</td>
						<td>
							Table Cell 3
						</td>
						<td>
							Table Cell 4
						</td>
						<td>
							Table Cell 5
						</td>
					</tr>
					<tr>
						<td>
							Table Cell 1
						</td>
						<td>
							Table Cell 2
						</td>
						<td>
							Table Cell 3
						</td>
						<td>
							Table Cell 4
						</td>
						<td>
							Table Cell 5
						</td>
					</tr>
					<tr>
						<td>
							Table Cell 1
						</td>
						<td>
							Table Cell 2
						</td>
						<td>
							Table Cell 3
						</td>
						<td>
							Table Cell 4
						</td>
						<td>
							Table Cell 5
						</td>
					</tr>
				</tbody>
				<tfoot>
					<tr>
						<th>
							Table Footer 1
						</th>
						<th>
							Table Footer 2
						</th>
						<th>
							Table Footer 3
						</th>
						<th>
							Table Footer 4
						</th>
						<th>
							Table Footer 5
						</th>
					</tr>
				</tfoot>
			</table>

			<h2>
				Headings
			</h2>
			<h1>
				Heading 1
			</h1>
			<h2>
				Heading 2
			</h2>
			<h3>
				Heading 3
			</h3>
			<h4>
				Heading 4
			</h4>
			<h5>
				Heading 5
			</h5>
			<h6>
				Heading 6
			</h6>


			<h2>
				Paragraphs
			</h2>
			<p>
				A paragraph (from the Greek paragraphos, “to write beside” or “written beside”) is a self-contained unit of a discourse in writing dealing with a particular point or idea. A paragraph consists of one or more sentences. Though not required by the syntax of any language, paragraphs are usually an expected part of formal writing, used to organize longer prose.
			</p>

			<h2>
				Blockquotes
			</h2>
			<blockquote>
				<p>
					A block quotation (also known as a long quotation or extract) is a quotation in a written document, that is set off from the main text as a paragraph, or block of text.
				</p>
				<p>
					It is typically distinguished visually using indentation and a different typeface or smaller size quotation. It may or may not include a citation, usually placed at the bottom.
				</p>
				<cite>
					<a href="#">
						Said no one, ever.
					</a>
				</cite>
			</blockquote>

			<h2>
				Lists
			</h2>

			<h3>
				Definition list
			</h3>
			<dl>
				<dt>
					Definition List Title
				</dt>
				<dd>
					This is a definition list division.
				</dd>
			</dl>

			<h3>
				Ordered List
			</h3>
			<ol>
				<li>
					List Item 1
				</li>
				<li>
					List Item 2
				</li>
				<li>
					List Item 3
				</li>
			</ol>

			<h3>
				Unordered List
			</h3>
			<ul>
				<li>
					List Item 1
				</li>
				<li>
					List Item 2
				</li>
				<li>
					List Item 3
				</li>
			</ul>

			<h2>
				Horizontal rules
			</h2>
			<hr>

			<h2>
				Tabular data
			</h2>
			<table>
				<caption>
					Table Caption
				</caption>
				<thead>
					<tr>
						<th>
							Table Heading 1
						</th>
						<th>
							Table Heading 2
						</th>
						<th>
							Table Heading 3
						</th>
						<th>
							Table Heading 4
						</th>
						<th>
							Table Heading 5
						</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>
							Table Cell 1
						</td>
						<td>
							Table Cell 2
						</td>
						<td>
							Table Cell 3
						</td>
						<td>
							Table Cell 4
						</td>
						<td>
							Table Cell 5
						</td>
					</tr>
					<tr>
						<td>
							Table Cell 1
						</td>
						<td>
							Table Cell 2
						</td>
						<td>
							Table Cell 3
						</td>
						<td>
							Table Cell 4
						</td>
						<td>
							Table Cell 5
						</td>
					</tr>
					<tr>
						<td>
							Table Cell 1
						</td>
						<td>
							Table Cell 2
						</td>
						<td>
							Table Cell 3
						</td>
						<td>
							Table Cell 4
						</td>
						<td>
							Table Cell 5
						</td>
					</tr>
					<tr>
						<td>
							Table Cell 1
						</td>
						<td>
							Table Cell 2
						</td>
						<td>
							Table Cell 3
						</td>
						<td>
							Table Cell 4
						</td>
						<td>
							Table Cell 5
						</td>
					</tr>
				</tbody>
				<tfoot>
					<tr>
						<th>
							Table Footer 1
						</th>
						<th>
							Table Footer 2
						</th>
						<th>
							Table Footer 3
						</th>
						<th>
							Table Footer 4
						</th>
						<th>
							Table Footer 5
						</th>
					</tr>
				</tfoot>
			</table>

			<h2>
				Code
			</h2>
			<p>
				<strong>Keyboard input:</strong> <kbd>Cmd</kbd>
			</p>
			<p>
				<strong>Inline code:</strong> <code>&lt;div&gt;code&lt;/div&gt;</code>
			</p>
			<p>
				<strong>Sample output:</strong> <samp>This is sample output from a computer program.</samp>
			</p>

			<h2>
				Pre-formatted text
			</h2>
			<pre>P R E F O R M A T T E D T E X T
! " # $ % &amp; ' ( ) * + , - . /
0 1 2 3 4 5 6 7 8 9 : ; &lt; = &gt; ?
@ A B C D E F G H I J K L M N O
P Q R S T U V W X Y Z [ \ ] ^ _
` a b c d e f g h i j k l m n o
p q r s t u v w x y z { | } ~ </pre>

			<h2>
				Inline elements
			</h2>
			<p>
				<a href="#">This is a text link</a>.
			</p>
			<p>
				<strong>Strong is used to indicate strong importance.</strong>
			</p>
			<p>
				<em>This text has added emphasis.</em>
			</p>
			<p>
				<del>This text is deleted</del> and <ins>This text is inserted</ins>.
			</p>
			<p>
				<s>This text has a strikethrough</s>.
			</p>
			<p>
				Superscript<sup>®</sup>.
			</p>
			<p>
				Subscript for things like H<sub>2</sub>O.
			</p>
			<p>
				Abbreviation: <abbr title="HyperText Markup Language">HTML</abbr>
			</p>
			<p>
				<q cite="https://developer.mozilla.org/en-US/docs/HTML/Element/q">This text is a short inline quotation.</q>
			</p>
			<p>
				<cite>This is a citation.</cite>
			</p>
			<p>
				The <dfn>dfn element</dfn> indicates a definition.
			</p>
			<p>
				The <mark>mark element</mark> indicates a highlight.
			</p>
			<p>
				The <var>variable element</var>, such as <var>x</var> = <var>y</var>.
			</p>
			<p>
				The time element: <time datetime="2013-04-06T12:32+00:00">2 weeks ago</time>
			</p>

			<h2>
				Formulářové prvky
			</h2>
			<p class="message">
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam autem, tempora itaque cum fuga, dignissimos dolorum facere ut harum voluptas distinctio perferendis minima incidunt consequatur magni dicta, fugiat. Velit, officia.
			</p>
			<p class="message message--ok">
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam autem, tempora itaque cum fuga, dignissimos dolorum facere ut harum voluptas distinctio perferendis minima incidunt consequatur magni dicta, fugiat. Velit, officia.
			</p>
			<p class="message message--error">
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam autem, tempora itaque cum fuga, dignissimos dolorum facere ut harum voluptas distinctio perferendis minima incidunt consequatur magni dicta, fugiat. Velit, officia.
			</p>
			<p class="message message--warning">
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam autem, tempora itaque cum fuga, dignissimos dolorum facere ut harum voluptas distinctio perferendis minima incidunt consequatur magni dicta, fugiat. Velit, officia.
			</p>
			<p>
				<label for="default" class="inp-label">
					Defaultní input s labelem
				</label>
				<span class="inp-fix">
					<input type="text" name="default" id="default" class="inp-text">
				</span>
			</p>
			<p>
				<label for="email" class="inp-label">
					E-mail input
				</label>
				<span class="inp-fix">
					<input type="email" name="email" id="email" class="inp-text">
				</span>
			</p>
			<p>
				<label for="password" class="inp-label">
					Password input
				</label>
				<span class="inp-fix">
					<input type="password" name="password" id="password" class="inp-text">
				</span>
			</p>
			<p>
				<label for="number" class="inp-label">
					Number input
				</label>
				<span class="inp-fix">
					<input type="number" name="number" id="number" class="inp-text">
				</span>
			</p>
			<p>
				<label for="tel" class="inp-label">
					Phone input
				</label>
				<span class="inp-fix">
					<input type="tel" name="tel" id="tel" class="inp-text">
				</span>
			</p>
			<p>
				<label for="number" class="inp-label">
					Number input
				</label>
				<span class="inp-fix">
					<input type="number" name="number" id="number" class="inp-text">
				</span>
			</p>
			<p>
				<label for="select" class="inp-label">
					Select
				</label>
				<span class="inp-fix">
					<select name="select" id="select" class="inp-select">
						<option value="">
							Vyberte hodnotu
						</option>
						<option value="1">
							Hodnota 1
						</option>
						<option value="2">
							Hodnota 2
						</option>
						<option value="3">
							Hodnota 3
						</option>
						<option value="4">
							Hodnota 4
						</option>
					</select>
				</span>
			</p>
			<p>
				<label for="textarea" class="inp-label">
					Textarea
				</label>
				<span class="inp-fix">
					<textarea name="textarea" id="textarea" class="inp-text" cols="40" rows="6"></textarea>
				</span>
			</p>
			<p>
				<label for="placeholder" class="inp-label u-vhide">
					Input s placeholderem a skrytým labelem
				</label>
				<span class="inp-fix">
					<input type="text" name="placeholder" id="placeholder" class="inp-text" placeholder="Input s placeholderem a skrytým labelem">
				</span>
			</p>
			<p>
				<label for="disabled" class="inp-label">
					Disabled defaultní input
				</label>
				<span class="inp-fix">
					<input type="text" name="disabled" id="disabled" class="inp-text" disabled>
				</span>
			</p>
			<p class="has-error">
				<label for="error" class="inp-label">
					Defaultní input s errorem
				</label>
				<span class="inp-fix">
					<input type="text" name="error" id="error" class="inp-text">
				</span>
			</p>
			<p>
				<button type="submit" class="btn">
					<span class="btn__text">
						Odeslat
					</span>
				</button>
			</p>

			<h2>
				Pomocné třídy
			</h2>
			<table>
				<thead>
					<tr>
						<th>
							Třída
						</th>
						<th>
							Popis a ukázka
						</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<th>
							.u-text-left
						</th>
						<td class="u-text-left">
							Zarovnání textu doleva
						</td>
					</tr>
					<tr>
						<th>
							.u-text-right
						</th>
						<td class="u-text-right">
							Zarovnání textu doprava
						</td>
					</tr>
					<tr>
						<th>
							.u-text-center
						</th>
						<td class="u-text-center">
							Zarovnání textu na střed
						</td>
					</tr>
					<tr>
						<th>
							.u-text-justify
						</th>
						<td class="u-text-justify">
							Zarovnání textu do bloku
						</td>
					</tr>
					<tr>
						<th>
							.u-text-nowrap
						</th>
						<td>
							Nezalomitelný text
						</td>
					</tr>
					<tr>
						<th>
							.u-text-truncate
						</th>
						<td>
							Ukončení dlouhého textu třemi tečkami
						</td>
					</tr>
					<tr>
						<th>
							.u-text-lowercase
						</th>
						<td class="u-text-lowercase">
							Malá písmena
						</td>
					</tr>
					<tr>
						<th>
							.u-text-uppercase
						</th>
						<td class="u-text-uppercase">
							Velká písmena
						</td>
					</tr>
					<tr>
						<th>
							.u-text-capitalize
						</th>
						<td class="u-text-capitalize">
							Velká pouze první písmena
						</td>
					</tr>
					<tr>
						<th>
							.u-font-light
						</th>
						<td class="u-font-light">
							Tenké písmo
						</td>
					</tr>
					<tr>
						<th>
							.u-font-regular
						</th>
						<td class="u-font-regular">
							Normální písmo
						</td>
					</tr>
					<tr>
						<th>
							.u-font-bold
						</th>
						<td class="u-font-bold">
							Tučné písmo
						</td>
					</tr>
					<tr>
						<th>
							.u-font-italic
						</th>
						<td class="u-font-italic">
							Kurzíva
						</td>
					</tr>
					<tr>
						<th>
							.u-text-hide
						</th>
						<td>
							Skrytí textu (např. při nahrazování obrázkem)
						</td>
					</tr>
					<tr>
						<th>
							.u-align-top
						</th>
						<td class="u-align-top">
							Zarovnání nahoru
						</td>
					</tr>
					<tr>
						<th>
							.u-align-middle
						</th>
						<td class="u-align-middle">
							Zarovnání na střed
						</td>
					</tr>
					<tr>
						<th>
							.u-align-bottom
						</th>
						<td class="u-align-bottom">
							Zarovnání dolů
						</td>
					</tr>
					<tr>
						<th>
							.u-pull-left
						</th>
						<td>
							Float vlevo
						</td>
					</tr>
					<tr>
						<th>
							.u-pull-right
						</th>
						<td>
							Float vpravo
						</td>
					</tr>
					<tr>
						<th>
							.u-center-block
						</th>
						<td>
							Centrování blocku
						</td>
					</tr>
					<tr>
						<th>
							.u-box-vertical
						</th>
						<td>
							Vertikální centrování uvnitř bloku
						</td>
					</tr>
					<tr>
						<th>
							.u-clearfix
						</th>
						<td>
							Clearování floatů
						</td>
					</tr>
					<tr>
						<th>
							.u-vhide
						</th>
						<td>
							Skrytí obsahu (zůstává zobrazitelný čtečkám)
						</td>
					</tr>
					<tr>
						<th>
							.u-js-hide
						</th>
						<td>
							Skrytí obsahu (pouze při zapnutém javascriptu)
						</td>
					</tr>
					<tr>
						<th>
							.u-out
						</th>
						<td>
							Vypozicování obsahu mimo viewport
						</td>
					</tr>
					<tr>
						<th>
							.u-js-out
						</th>
						<td>
							Vypozicování obsahu mimo viewport (pouze při zapnutém javascriptu)
						</td>
					</tr>
					<tr>
						<th>
							.u-hide
						</th>
						<td>
							Skrytí obsahu
						</td>
					</tr>
					<tr>
						<th>
							.u-show
						</th>
						<td>
							Zobrazení obsahu
						</td>
					</tr>
					<tr>
						<th>
							.u-hide@sm
						</th>
						<td>
							Skrytí obsahu na všech breakpointech větších než SM (většinou &lt; 480px)
						</td>
					</tr>
					<tr>
						<th>
							.u-show@sm
						</th>
						<td>
							Zobrazení obsahu na všech breakpointech větších než SM (většinou &lt; 480px)
						</td>
					</tr>
					<tr>
						<th>
							.u-hide@md
						</th>
						<td>
							Skrytí obsahu na všech breakpointech větších než MD (většinou &lt; 750px)
						</td>
					</tr>
					<tr>
						<th>
							.u-show@md
						</th>
						<td>
							Zobrazení obsahu na všech breakpointech větších než MD (většinou &lt; 750px)
						</td>
					</tr>
					<tr>
						<th>
							.u-hide@lg
						</th>
						<td>
							Skrytí obsahu na všech breakpointech větších než LG (většinou &lt; 1000px)
						</td>
					</tr>
					<tr>
						<th>
							.u-show@lg
						</th>
						<td>
							Zobrazení obsahu na všech breakpointech větších než LG (většinou &lt; 1000px)
						</td>
					</tr>
					<tr>
						<th>
							.u-hide@xl
						</th>
						<td>
							Skrytí obsahu na všech breakpointech větších než XL (většinou &lt; 1200px)
						</td>
					</tr>
					<tr>
						<th>
							.u-show@xl
						</th>
						<td>
							Zobrazení obsahu na všech breakpointech větších než XL (většinou &lt; 1200px)
						</td>
					</tr>
					<tr>
						<th>
							.u-no-print
						</th>
						<td>
							Skrytí obsahu pro tisk
						</td>
					</tr>
				</tbody>
			</table>

			<h2>
				Grid
			</h2>
			{% set breakpoints = { 'xs': 'Defaultní grid (mobil portrait)', 'sm' : 'Grid od breakpointu sm (mobil landscape)', 'md' : 'Grid od breakpointu md (tablet)', 'lg' : 'Grid od breakpointu lg (desktop)', 'xl' : 'Grid od breakpointu xl (desktop – large)'} %}
			{% for breakpoint, title in breakpoints %}
				<h2>
					{{ title }}
				</h2>
				<div class="grid">
				{% for grid in range(1,12) %}

						{% if grid <= 6 %}
							{% set iterations = 12/grid %}
						{% else %}
							{% set iterations = 1 %}
						{% endif %}
						{% for item in range(1,iterations) %}
							<div class="grid__cell{% if grid != 12 %} size--{{grid}}-12{% if breakpoint != 'xs' %}@{{breakpoint}}{% endif %}{% endif %}">
								<p style="background: #f0f0f0; padding: 20px; text-align: center;">
									{{grid}}/12
								</p>
							</div>
							{% if grid == 5 %}
								<div class="grid__cell size--7-12{% if breakpoint != 'xs' %}@{{breakpoint}}{% endif %}">
									<p style="background: #f0f0f0; padding: 20px; text-align: center;">
										7/12
									</p>
								</div>
							{% elseif grid == 7 %}
								<div class="grid__cell size--5-12{% if breakpoint != 'xs' %}@{{breakpoint}}{% endif %}">
									<p style="background: #f0f0f0; padding: 20px; text-align: center;">
										5/12
									</p>
								</div>
							{% elseif grid == 8 %}
								<div class="grid__cell size--4-12{% if breakpoint != 'xs' %}@{{breakpoint}}{% endif %}">
									<p style="background: #f0f0f0; padding: 20px; text-align: center;">
										4/12
									</p>
								</div>
							{% elseif grid == 9 %}
								<div class="grid__cell size--3-12{% if breakpoint != 'xs' %}@{{breakpoint}}{% endif %}">
									<p style="background: #f0f0f0; padding: 20px; text-align: center;">
										3/12
									</p>
								</div>
							{% elseif grid == 10 %}
								<div class="grid__cell size--2-12{% if breakpoint != 'xs' %}@{{breakpoint}}{% endif %}">
									<p style="background: #f0f0f0; padding: 20px; text-align: center;">
										2/12
									</p>
								</div>
							{% elseif grid == 11 %}
								<div class="grid__cell size--1-12{% if breakpoint != 'xs' %}@{{breakpoint}}{% endif %}">
									<p style="background: #f0f0f0; padding: 20px; text-align: center;">
										1/12
									</p>
								</div>
							{% endif %}
						{% endfor %}

				{% endfor %}
				</div>
			{% endfor %}

			<h2>
				Auto šířka buňek gridu
			</h2>
			<div class="grid">
				{% for grid in range(0,3) %}
					<div class="grid__cell size--auto">
						<p>
							Lorem ipsum
						</p>
					</div>
					<div class="grid__cell size--auto">
						<p>
							Dolor sit amet
						</p>
					</div>
				{% endfor %}
			</div>

			<h2>
				Auto šírka buňky gridu s vyplněním celého řádku
			</h2>
			<div class="grid">
				<div class="grid__cell size--autogrow">
					<p>
						Lorem ipsum
					</p>
				</div>
				<div class="grid__cell size--autogrow">
					<p>
						Dolor sit amet
					</p>
				</div>
			</div>

			<h2>
				Horizontální zarovnání
			</h2>
			<div class="grid">
				<div class="grid__cell size--4-12">
					<p style="background: #f0f0f0; padding: 20px;">
						<strong>
							Defaultní - left
						</strong>
					</p>
				</div>
			</div>
			<div class="grid grid--center">
				<div class="grid__cell size--4-12">
					<p style="background: #f0f0f0; padding: 20px;">
						<strong>
							Defaultní - center
						</strong>
					</p>
				</div>
			</div>
			<div class="grid grid--right">
				<div class="grid__cell size--4-12">
					<p style="background: #f0f0f0; padding: 20px;">
						<strong>
							Defaultní - right
						</strong>
					</p>
				</div>
			</div>
			<div class="grid grid--space-between">
				<div class="grid__cell size--3-12">
					<p style="background: #f0f0f0; padding: 20px;">
						<strong>
							Defaultní - space-between
						</strong>
					</p>
				</div>
				<div class="grid__cell size--3-12">
					<p style="background: #f0f0f0; padding: 20px;">
						Lorem ipsum dolor
					</p>
				</div>
			</div>

			<h2>
				Vertikální zarovnání
			</h2>
			<div class="grid">
				<div class="grid__cell size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						<strong>
							Defaultní – top
						</strong>
					</p>
				</div>
				<div class="grid__cell size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
					</p>
				</div>
			</div>
			<div class="grid grid--middle">
				<div class="grid__cell size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						<strong>
							Defaultní – middle
						</strong>
					</p>
				</div>
				<div class="grid__cell size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
					</p>
				</div>
			</div>
			<div class="grid grid--bottom">
				<div class="grid__cell size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						<strong>
							Defaultní – bottom
						</strong>
					</p>
				</div>
				<div class="grid__cell size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
					</p>
				</div>
			</div>

			<h3>
				Vertikální zarovnání na buňkách
			</h3>
			<div class="grid grid--middle">
				<div class="grid__cell grid__cell--top size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						<strong>
							Defaultní – top
						</strong><br>
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
					</p>
				</div>
				<div class="grid__cell size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae.
					</p>
				</div>
			</div>
			<div class="grid">
				<div class="grid__cell grid__cell--middle size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						<strong>
							Defaultní – middle
						</strong><br>
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
					</p>
				</div>
				<div class="grid__cell size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae.
					</p>
				</div>
			</div>
			<div class="grid">
				<div class="grid__cell grid__cell--bottom size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						<strong>
							Defaultní – bottom
						</strong><br>
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
					</p>
				</div>
				<div class="grid__cell size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae.
					</p>
				</div>
			</div>

			<h2>
				Srovnání výšek následující prvku v buňce
			</h2>
			<div class="grid">
				<div class="grid__cell grid__cell--eq size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
					</p>
				</div>
				<div class="grid__cell size--6-12">
					<p style="background: #f0f0f0; padding: 20px;">
						Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae.
					</p>
				</div>
			</div>

			<h2>
				Pořadí sloupců
			</h2>
			<div class="grid">
				<div class="grid__cell size--1-12 order--12">
					<p style="background: #f0f0f0; padding: 20px;">
						1 -> 12
					</p>
				</div>
				<div class="grid__cell size--1-12 order--11">
					<p style="background: #f0f0f0; padding: 20px;">
						2 -> 11
					</p>
				</div>
				<div class="grid__cell size--1-12 order--10">
					<p style="background: #f0f0f0; padding: 20px;">
						3 -> 10
					</p>
				</div>
				<div class="grid__cell size--1-12 order--9">
					<p style="background: #f0f0f0; padding: 20px;">
						4 -> 9
					</p>
				</div>
				<div class="grid__cell size--1-12 order--8">
					<p style="background: #f0f0f0; padding: 20px;">
						5 -> 8
					</p>
				</div>
				<div class="grid__cell size--1-12 order--7">
					<p style="background: #f0f0f0; padding: 20px;">
						6 -> 7
					</p>
				</div>
				<div class="grid__cell size--1-12 order--9">
					<p style="background: #f0f0f0; padding: 20px;">
						7 -> 9
					</p>
				</div>
				<div class="grid__cell size--1-12 order--5">
					<p style="background: #f0f0f0; padding: 20px;">
						8 -> 5
					</p>
				</div>
				<div class="grid__cell size--1-12 order--4">
					<p style="background: #f0f0f0; padding: 20px;">
						9 -> 4
					</p>
				</div>
				<div class="grid__cell size--1-12 order--3">
					<p style="background: #f0f0f0; padding: 20px;">
						10 -> 3
					</p>
				</div>
				<div class="grid__cell size--1-12 order--2">
					<p style="background: #f0f0f0; padding: 20px;">
						11 -> 2
					</p>
				</div>
				<div class="grid__cell size--1-12 order--1">
					<p style="background: #f0f0f0; padding: 20px;">
						12 -> 1
					</p>
				</div>
			</div>
		</div>
	</main>

{% endblock %}
