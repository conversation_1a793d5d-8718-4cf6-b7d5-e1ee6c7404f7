/* stylelint-disable CssSyntaxError */
/* stylelint-disable selector-no-qualifying-type */
@use 'config';
@use 'base/variables' as variables;
@use 'base/mixins' as mixins;
@use 'sass:math';

.b-modal {
	$s: &;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: -1;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	padding: variables.$row-main-gutter;
	visibility: hidden;
	opacity: 0;
	transition: opacity 0.3s, z-index 0s 0.3s, visibility 0s 0.3s;
	// modifiers
	&--small {
		#{$s}__wrapper {
			max-width: variables.$box-width-small;
		}
	}

	&--medium {
		#{$s}__wrapper {
			max-width: variables.$modal-width-medium;
		}
	}

	// elements
	&__wrapper {
		position: relative;
		z-index: 2;
		display: grid;
		grid-template-columns: 1fr auto;
		grid-template-rows: 60px 1fr;
		/* stylelint-disable-next-line declaration-block-no-redundant-longhand-properties */
		grid-template-areas:
			'content content'
			'content content';
		flex-grow: 1;
		max-width: #{variables.$row-main-width - 2 * variables.$row-main-gutter};
		max-height: 100%;
		border-radius: variables.$modal-radius;
		overflow: hidden;
	}
	&__header {
		position: relative;
		z-index: 11;
		display: flex;
		grid-area: 1 / 2 / 2 / 2;
		justify-content: flex-end;
	}
	&__title {
		z-index: 2;
		display: none;
		grid-area: title;
		justify-content: center;
		align-items: center;
		padding: 0 0 0 52px;
		background: variables.$color-white;
		h3 {
			margin: 0;
		}
	}
	&__description {
		display: none;
	}
	&__content {
		position: relative;
		z-index: 2;
		display: flex;
		grid-area: content;
		min-height: 100px;
		padding: 40px 30px; // padding for text content modal
		background: variables.$color-white;
		overflow: hidden;
	}
	&__slide {
		display: flex;
		flex: 0 0 100%;
		align-items: flex-start;
		order: 2;
		max-height: 100%;
		overflow: hidden;
		overflow-y: auto;
		opacity: 0;
		transition: opacity 0.3s, z-index 0s 0.3s;
		overscroll-behavior: contain;
		&.is-active {
			z-index: 2;
			order: 1;
			opacity: 1;
			transition: opacity 0.3s, z-index 0s;
		}
	}
	&__image,
	&__video {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100%;
		> * {
			flex: 0 0 auto;
			width: auto;
			max-width: 100%;
			height: auto;
			max-height: 100%;
		}
		&--multiple {
			> * {
				max-width: 50%;
			}
		}
	}
	&__image img {
		user-select: none;
	}
	&__inner {
		width: 100%;
		body:not(.is-loading) & {
			background: #ffffff;
		}
	}
	&__iframe {
		height: 100%;
		padding: 20px;
		background: #ffffff;
		iframe {
			width: 100%;
			height: 100%;
		}
	}
	&__embed {
		position: relative;
		width: 100%;
		height: 100%;
		overflow: hidden;
		&::before {
			content: '';
			display: block;
			padding-top: percentage(calc(9 / 16));
		}
		iframe {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
		}
	}
	&__nav {
		display: none;
		grid-area: nav;
		border-left: 1px solid variables.$color-bd;
		background: variables.$color-bg;
	}
	&__loader {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 10;
		display: none;
		align-items: center;
		color: #ffffff;
		font-size: 30px;
		justify-items: center;
		& .icon-load {
			width: 40px;
			animation-name: loader;
			animation-duration: 1s;
			animation-timing-function: linear;
			animation-iteration-count: infinite;
		}
	}
	&__loader-icon {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 40px;
		border-radius: 50%;
		background: rgba(0, 0, 0, 0.5);
		animation-name: loader;
		animation-duration: 1s;
		animation-timing-function: linear;
		animation-iteration-count: infinite;
		& > span {
			width: 40px;
			height: 40px;
			line-height: 40px;
			text-align: center;
		}
	}
	&__bg {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 1;
		background-color: variables.$modal-overlay;
	}
	&__prev,
	&__next {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 20px;
		z-index: 3;
		display: flex;
		align-items: center;
		pointer-events: none;
	}
	&__prev {
		justify-content: flex-start;
	}
	&__next {
		right: 20px;
		left: auto;
		justify-content: flex-end;
	}
	&__prev-btn,
	&__next-btn {
		@include mixins.button-reset;
		display: inline-block;
		width: 50px;
		height: 50px;
		border: 1px solid variables.$color-bd;
		background-color: variables.$color-white;
		color: variables.$color-link;
		text-align: center;
		transition: color variables.$t, background-color variables.$t, border-color variables.$t;
		pointer-events: auto;
		.hoverevents &:hover {
			border-color: variables.$color-primary;
			background-color: variables.$color-primary;
			color: variables.$color-white;
			cursor: pointer;
		}
		.icon-svg {
			width: 9px;
			height: 14px;
		}
	}
	&__close {
		@include mixins.button-reset;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 42px;
		height: 42px;
		padding: 10px;
		color: variables.$color-sg-black;
		outline: none;
		transition: color variables.$t;
		cursor: pointer;
		&:first-child {
			margin: 0;
		}
		.icon-svg {
			width: 14px;
			height: 14px;
		}
	}

	&__message {
		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: -1;
			background-color: inherit;
		}

		&--error {
			background: variables.$color-sg-red;
		}
		&--ok {
			background: variables.$color-sg-green;
		}
		&--warning {
			background: variables.$color-bg-light;
		}
	}

	// gallery thumbnails
	&__tabs {
		display: grid;
		grid-template-rows: auto 1fr;
		height: 100%;
	}
	&__tab-content {
		position: relative;
		min-height: 0;
		padding: 28px 30px;
		overflow: hidden;
	}
	&__thumbs-wrap {
		height: 100%;
	}
	&__thumbs {
		display: grid;
		grid-template-columns: 1fr 1fr;
		grid-template-rows: auto;
		max-height: 100%;
		margin: 0 0 -8px -8px;
	}
	&__thumb {
		position: relative;
		margin: 0 0 8px 8px;
		cursor: pointer;
		&::before {
			content: '';
			display: block;
			padding-top: 100%;
		}
	}
	&__thumb-img {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border: 1px solid variables.$color-bd;
		border-radius: 1px;
		transition: border-color variables.$t;
		object-fit: cover;
		&.is-active {
			border-color: variables.$color-secondary;
		}
		// .hoverevents &:hover {
		// 	border-color: variables.$color-secondary-dark-hover;
		// }
	}
	&__thumbs-prev,
	&__thumbs-next {
		@include mixins.button-reset;
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 40px;
		background: rgba(variables.$color-white, 0.75);
		&.is-disabled {
			display: none;
		}
	}
	&__thumbs-next {
		top: auto;
		bottom: 0;
	}

	// MODIF
	&--gallery {
		#{$s} {
			&__wrapper {
				grid-template-columns: 1fr auto 265px;
				grid-template-rows: 104px 1fr;
				/* stylelint-disable-next-line declaration-block-no-redundant-longhand-properties */
				grid-template-areas:
					'title header nav'
					'content content nav';
				height: 100%;
			}
			&__header {
				grid-area: header;
				background: variables.$color-white;
			}
			&__title {
				display: flex;
				align-items: flex-start;
				padding-top: 55px;
			}
			&__slide {
				position: absolute;
				top: 30px;
				right: 0;
				bottom: 30px;
				left: 0;
				z-index: 1;
				align-items: center;
			}
			&__embed {
				display: flex;
				align-items: center;
				&::before {
					display: none;
				}
				iframe {
					position: relative;
					height: auto;
					aspect-ratio: 850 / 480;
				}
			}
			&__nav {
				display: block;
			}
			&__next {
				right: 285px;
			}
			&__prev,
			&__next {
				top: 105px;
			}
		}
	}
	&--info &__wrapper {
		max-width: math.round(
			(calc((variables.$row-main-width - variables.$row-main-gutter) / variables.$grid-columns) * 8) - variables.$row-main-gutter
		);
	}
	&--custom {
		background-color: rgba(variables.$color-primary, 0.5);
		#{$s}__inner {
			position: relative;
			max-width: variables.$modal-width-medium;
		}
		#{$s}__close {
			position: absolute;
			top: 0;
			right: 0;
			z-index: 10;
		}
		#{$s}__content {
			flex-wrap: wrap;
		}
		#{$s}__title {
			display: block;
			flex: 0 1 100%;
			padding: 0;
			text-align: center;
		}
		#{$s}__text {
			flex: 0 1 100%;
			padding: 0;
			text-align: center;
		}
	}

	// STATES
	&.is-opened {
		z-index: 1000;
		visibility: visible;
		opacity: 1;
		/* stylelint-disable-next-line prettier/prettier */
		transition: opacity 0.3s, z-index 0s, visibility 0s;
	}

	body.is-loading &__wrapper,
	/* stylelint-disable-next-line selector-no-qualifying-type */
	body.is-loading &__bg {
		display: none;
	}
	body.is-loading &__loader {
		display: grid;
	}
	&.is-first {
		& .b-modal__prev {
			display: none;
		}
	}
	&.is-last {
		& .b-modal__next {
			display: none;
		}
	}

	// HOVERS
	// .hoverevents &__close:hover {
	// 	color: variables.$color-secondary-dark-hover;
	// }

	// MQ
	@media (config.$md-down) {
		padding: variables.$modal-padding;
		&__nav {
			display: none;
		}
		&__prev,
		&__next {
			top: 0;
			left: 10px;
		}
		&__next {
			right: 10px;
			left: auto;
		}
		&__prev-btn,
		&__next-btn {
			width: 40px;
			height: 40px;
		}
		&__close {
			&:first-child {
				// margin: 15px 12px 0 0;
				background-color: rgba(variables.$color-white, 0.7);
			}
		}
		&__content {
			padding: 30px;
		}
		&--gallery {
			#{$s} {
				&__wrapper {
					grid-template-columns: 5fr 1fr;
					grid-template-rows: 60px 1fr;
					/* stylelint-disable-next-line declaration-block-no-redundant-longhand-properties */
					grid-template-areas:
						'title header'
						'content content';
				}
				&__nav {
					display: none;
				}
				&__next {
					right: 10px;
					left: auto;
				}
				&__title {
					h3 {
						display: -webkit-box;
						overflow: hidden;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
					}
				}
			}
		}
	}
	@media (config.$sm-down) {
		&__prev,
		&__next {
			left: 20px;
		}
		&__next {
			right: 20px;
		}
		&__prev-btn,
		&__next-btn {
			width: 30px;
			height: 30px;
		}
		&__content {
			padding: 40px 20px;
		}
		&--gallery {
			#{$s} {
				&__prev,
				&__next {
					opacity: 0.5;
				}
				&__prev {
					left: 10px;
				}
			}
		}
	}
	@media (config.$sm-up) {
		&--gallery {
			#{$s} {
				&__slide {
					right: 60px;
					left: 60px;
				}
			}
		}
	}
	@media (config.$md-up) {
		&--gallery {
			#{$s} {
				&__slide {
					right: 90px;
					left: 90px;
				}
				&__prev {
					left: 30px;
				}
				&__next {
					right: 295px;
				}
			}
		}
	}
	@media (config.$lg-up) {
		padding: 50px;
		&--gallery {
			#{$s} {
				&__slide {
					right: 140px;
					left: 140px;
				}
				&__prev {
					left: 60px;
				}
				&__next {
					right: 325px;
				}
			}
		}
	}
}

@keyframes loader {
	from {
		transform: rotate(360deg);
	}
	to {
		transform: rotate(0deg);
	}
}
