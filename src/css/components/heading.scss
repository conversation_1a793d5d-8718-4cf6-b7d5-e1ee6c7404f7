@use 'config';
@use 'base/variables';

.c-heading-h2 {
	display: flex;
	gap: 10px 30px;
	flex-wrap: wrap;
	margin-bottom: 30px;
	padding-bottom: 15px;
	border-bottom: 1px solid variables.$color-secondary-light;
	font-size: 22px;

	h2,
	h3 {
		flex-grow: 1;
		margin: 0;
		font-size: inherit;
	}

	&__more {
		align-self: center;
		font-size: 14px;
		justify-self: flex-end;
	}

	&__link {
		position: relative;
		flex-shrink: 0;
		padding-right: 20px;

		&::after {
			content: '';
			position: absolute;
			top: 50%;
			right: 3px;
			width: 6px;
			height: 11px;
			background: url(#{variables.$svg-chevron-right}) no-repeat center;
			transform: translateY(-50%);
		}
	}

	@media (config.$xl-up) {
		margin-bottom: 40px;
	}
}
