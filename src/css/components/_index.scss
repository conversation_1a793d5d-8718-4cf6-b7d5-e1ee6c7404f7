@forward 'components/message';
@forward 'components/heading';
@forward 'components/pagination';
@forward 'components/flag';

// Box
// @forward 'components/box/std'; nahradene za article
@forward 'components/box/collapse';
// Box - basic
@forward 'components/box/files';
@forward 'components/box/images';
@forward 'components/box/videos';
// Box - content
@forward 'components/box/annot';
@forward 'components/box/article';
@forward 'components/box/banner';
@forward 'components/box/contacts';
@forward 'components/box/cover';
@forward 'components/box/cookie';
@forward 'components/box/list';
@forward 'components/box/login';
@forward 'components/box/logo';
@forward 'components/box/map';
@forward 'components/box/menu-toggle';
@forward 'components/box/modal';
@forward 'components/box/share-buttons';
@forward 'components/box/system-messages';
@forward 'components/box/main-nav';
@forward 'components/box/gift-form';
@forward 'components/box/suggest';
@forward 'components/box/carousel';
@forward 'components/box/toggle';
@forward 'components/box/video';

// Box - catalog
@forward 'components/box/awards';
@forward 'components/box/basket-small';
@forward 'components/box/category';
@forward 'components/box/coupon';
@forward 'components/box/favorite';
@forward 'components/box/parameters';
@forward 'components/box/prebasket';
@forward 'components/box/product';
@forward 'components/box/products-suggest';
@forward 'components/box/product-detail';
@forward 'components/box/product-gallery';
@forward 'components/box/product-order';
@forward 'components/box/steps';
@forward 'components/box/benefit';
@forward 'components/box/hp-category';
@forward 'components/box/hp-ad';

@forward 'components/box/cart-summary';
@forward 'components/box/order-del-pay';
@forward 'components/box/order-buttons';
@forward 'components/box/order-login';

// Crossroad
@forward 'components/crossroad/articles';
@forward 'components/crossroad/basket';
@forward 'components/crossroad/grid';
@forward 'components/crossroad/products';
@forward 'components/crossroad/orders';
@forward 'components/crossroad/branches';
@forward 'components/crossroad/benefits';
@forward 'components/crossroad/categories';

// Form
@forward 'components/form/basket';
@forward 'components/form/contact';
@forward 'components/form/delivery';
@forward 'components/form/filter';
@forward 'components/form/open';
@forward 'components/form/search';
@forward 'components/form/sort';

// Menu
@forward 'components/menu/accessibility';
@forward 'components/menu/breadcrumb';
@forward 'components/menu/footer';
@forward 'components/menu/main';
@forward 'components/menu/partners-image';
@forward 'components/menu/social-networks';
@forward 'components/menu/login';
@forward 'components/menu/header-toggle';
@forward 'components/menu/submenu';
@forward 'components/menu/search-results';
