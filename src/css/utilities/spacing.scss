@use 'config';
@use 'base/functions';

.u-mb-0 {
	margin-bottom: 0;
}

.u-mb-xs {
	margin-bottom: functions.spacing('xs');
}
.u-mt-xs {
	margin-top: functions.spacing('xs');
}

.u-mb-sm {
	margin-bottom: functions.spacing('sm');
}

.u-mt-sm {
	margin-top: functions.spacing('sm');
}

.u-mb-md {
	margin-bottom: 30px;

	@media (config.$lg-up) {
		margin-bottom: functions.spacing('md');
	}
}

.u-mt-md {
	margin-top: 30px;

	@media (config.$lg-up) {
		margin-top: functions.spacing('md');
	}
}

.u-mt-lg {
	margin-top: functions.spacing('md');

	// MQ
	@media (config.$lg-up) {
		margin-top: functions.spacing('lg');
	}
}

.u-mb-lg {
	margin-bottom: functions.spacing('md');

	// MQ
	@media (config.$lg-up) {
		margin-bottom: functions.spacing('lg');
	}
}

.u-pt-sm {
	padding-top: functions.spacing('sm');

	// MQ
	@media (config.$lg-up) {
		padding-top: functions.spacing('md');
	}
}

.u-pt-lg {
	padding-top: functions.spacing('md');

	// MQ
	@media (config.$lg-up) {
		padding-top: functions.spacing('lg');
	}
}

.u-pb-sm {
	padding-bottom: functions.spacing('sm');

	// MQ
	@media (config.$lg-up) {
		padding-bottom: functions.spacing('md');
	}
}

.u-mb-remove-last-child {
	& > :last-child {
		margin-bottom: 0;
	}
}
