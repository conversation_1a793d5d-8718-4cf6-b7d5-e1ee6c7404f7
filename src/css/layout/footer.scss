@use 'config';
@use 'base/variables';

.footer {
	background-color: variables.$color-bg-light;

	&__content {
		padding: 0 0 30px;
		&__grid {
			display: grid;
			grid-template-columns: repeat(1, minmax(0, 1fr));
			grid-template-rows: auto;
			grid-gap: 30px;
		}
	}

	&__copyright {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding-top: 30px;
		padding-bottom: 40px;
		border-top: 1px solid #f3ebdf;

		p {
			margin: 0 0 10px;
		}

		& > * {
			margin-bottom: 20px;
		}
	}

	.m-partners-image {
		flex-shrink: 0;
		width: 100%;
	}

	.b-copyright-texts {
		flex: 1;
	}

	.m-social-networks {
		margin-bottom: 8px;
	}

	@media (config.$md-up) {
		&__content {
			&__grid {
				grid-template-columns: repeat(2, minmax(0, 1fr));
			}
		}

		.m-partners-image {
			width: auto;
			padding-left: 30px;
		}
	}
	@media (config.$lg-up) {
		&__content {
			&__grid {
				grid-template-columns: repeat(4, minmax(0, 1fr));
			}
		}

		.m-partners-image {
			width: auto;
			padding-left: 100px;
		}
	}
}
