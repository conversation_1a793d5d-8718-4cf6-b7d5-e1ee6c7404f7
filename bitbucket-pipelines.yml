image:
  name: 368257470444.dkr.ecr.eu-central-1.amazonaws.com/php:8.1
  aws:
    access-key: $AWS_ECR_ACCESS_KEY_ID
    secret-key: $AWS_ECR_SECRET_ACCESS_KEY

definitions:
  caches:
    node-new-admin: www/admin/new/node_modules

pipelines:
  default:
    - step: &composer-install
        name: Install dependencies
        script:
          - php composer.phar install
        caches:
          - composer
        artifacts:
          - vendor/**

    - parallel: &quality-control
        - step:
            name: PHP Linter
            script:
              - php composer.phar run-script lint

        - step:
            name: Latte Linter
            script:
              - php composer.phar run-script latte-lint

        - step:
            name: Static Analysis
            script:
              - php composer.phar run-script phpstan

  branches:
    master:
      - step: *composer-install
      - parallel: *quality-control

      - parallel:
        - step:
            name: Install production dependencies
            script:
              - php composer.phar install --no-dev --classmap-authoritative
            caches:
              - composer
            artifacts:
              download: false
              paths:
                - vendor/**

        - step:
            name: Build front-end
            image: node:16
            script:
              - npm install
              - npm run build
            caches:
              - node
            artifacts:
              - www/static/**

        - step:
            name: Build new admin
            image: node:12
            script:
              - cd www/admin/new
              - npm install
              - npm run build
            caches:
              - node-new-admin
            artifacts:
              - www/admin/new/dist/**

      - step:
          name: Deploy to stage
          image:
            name: 368257470444.dkr.ecr.eu-central-1.amazonaws.com/docker-php-deployer:v7.0.0-rc.8
            aws:
              access-key: $AWS_ECR_ACCESS_KEY_ID
              secret-key: $AWS_ECR_SECRET_ACCESS_KEY
          deployment: stage
          script:
            # prepare SSH key
            - mkdir -p ~/.ssh
            - (umask 077; echo $DEPLOYMENT_KEY | base64 -d > ~/.ssh/id_ed25519)

            # mark version
            - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $BITBUCKET_COMMIT > app/config/webVersion.neon'

            # run deployer
            - dep -f .deploy.php deploy
