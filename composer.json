{"name": "superkoders/superadmin", "type": "project", "provide": {"ext-redis": "*"}, "require": {"php": "^8.1", "ext-gd": "*", "ext-json": "*", "azuyalabs/yasumi": "^2.3", "contributte/apitte": "^0.9.1", "contributte/application": "^0.5.1", "contributte/console": "^0.9.1", "contributte/console-extra": "^0.7.1", "contributte/elastica": "^1.0", "contributte/logging": "^0.6.1", "contributte/monolog": "^0.5.0", "contributte/validator": "^1.0", "dibi/dibi": "^4.1", "firebase/php-jwt": "^6.1", "fmasa/messenger": "^2.0", "gopay/payments-sdk-php": "^1.4", "guzzlehttp/psr7": "^1.8.3", "heureka/overeno-zakazniky": "^4.0", "jaybizzle/crawler-detect": "^1.2", "keltuo/php-smartemailing": "^1.0", "latte/latte": "^3.0", "league/csv": "^9.6", "marc-mabe/php-enum": "^4.3", "mpdf/mpdf": "^8.0", "nette/application": "^3.1", "nette/bootstrap": "^3.1", "nette/caching": "^3.1", "nette/component-model": "^3.0", "nette/di": "^3.0", "nette/finder": "^2.5", "nette/forms": "^3.1", "nette/http": "^3.1", "nette/mail": "^3.1", "nette/robot-loader": "^3.3", "nette/safe-stream": "^2.4", "nette/security": "^3.1", "nette/utils": "^3.2", "nettrine/annotations": "^0.7.0", "nettrine/cache": "^0.3.0", "nextras/migrations": "^3.1", "nextras/orm": "^4.0", "pelago/emogrifier": "^6.0", "php-curl-class/php-curl-class": "^9.0", "predis/predis": "^2.0", "sentry/sdk": "^3.1", "symfony/lock": "^6.0", "symfony/redis-messenger": "^6.0", "symfony/validator": "^6.0", "texy/texy": "^3.1", "tracy/tracy": "^2.8", "ublaboo/datagrid": "^6.9.5"}, "require-dev": {"deployer/deployer": "^7.0@rc", "mockery/mockery": "^1.4", "nette/tester": "^2.4", "nextras/orm-phpstan": "^1.0", "ninjify/coding-standard": "^0.12", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.2", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "roave/security-advisories": "dev-latest"}, "autoload": {"classmap": ["app/Bootstrap.php"], "files": ["app/bd.php"]}, "config": {"platform": {"php": "8.1.4"}, "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true}, "process-timeout": 600, "preferred-install": "dist", "github-protocols": ["https", "ssh"], "secure-http": false}, "scripts": {"phpstan": "phpstan analyse -c .phpstan.neon --memory-limit=2G", "lint": "parallel-lint --blame app tests --exclude tests/var", "latte-lint": "latte-lint app", "cs": "phpcs --standard=.ruleset.xml", "cs-fix": "phpcbf --standard=.ruleset.xml"}}