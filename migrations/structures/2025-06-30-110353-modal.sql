DROP TABLE IF EXISTS `modal`;
CREATE TABLE IF NOT EXISTS `modal` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`internalName` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`customFields<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	PRIMARY KEY (`id`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;

DROP TABLE IF EXISTS `modal_localization`;
CREATE TABLE IF NOT EXISTS `modal_localization` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`modalId` int(11) NOT NULL,
	`mutationId` int(11) NOT NULL,
	`name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
	`nameAnchor` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
	`nameTitle` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
	`description` text COLLATE utf8mb4_unicode_520_ci,
	`keywords` text COLLATE utf8mb4_unicode_520_ci,
	`public` int(11) DEFAULT NULL,
	`publicFrom` datetime DEFAULT NULL,
	`publicTo` datetime DEFAULT NULL,
	`isMain` int(11) NOT NULL DEFAULT '0',
	`edited` int(11) DEFAULT NULL,
	`editedTime` datetime DEFAULT NULL,
	`customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	`customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	PRIMARY KEY (`id`) USING BTREE,
	KEY `FK_modal_localization_mutation` (`mutationId`) USING BTREE,
	KEY `FK_modal_localization_modal` (`modalId`) USING BTREE,
	CONSTRAINT `FK_modal_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_modal_modal_parent` FOREIGN KEY (`modalId`) REFERENCES `modal` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
