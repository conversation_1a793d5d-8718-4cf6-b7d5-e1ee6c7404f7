CREATE TABLE IF NOT EXISTS `product_watchdog` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `productId` int(11) NOT NULL,
    `userId` int(11) NULL,
    `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `type` enum('in_stock','price_drop','price_under') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `targetPrice` decimal(10,2) NULL,
    `active` tinyint(1) NOT NULL DEFAULT 1,
    `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `notifiedAt` datetime NULL,
    `lastCheckedAt` datetime NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `FK_product_watchdog_product` (`productId`) USING BTREE,
    KEY `FK_product_watchdog_user` (`userId`) USING BTREE,
    <PERSON>EY `idx_product_watchdog_active` (`active`) USING BTREE,
    <PERSON>EY `idx_product_watchdog_type` (`type`) USING BTREE,
    KEY `idx_product_watchdog_email` (`email`) USING BTREE,
    CONSTRAINT `FK_product_watchdog_product` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_product_watchdog_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
