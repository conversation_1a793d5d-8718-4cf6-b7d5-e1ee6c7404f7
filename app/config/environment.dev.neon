parameters:
	stageName: 'stage'
	config:
		isDev: true
		env: local

		esBaseName: %config.projectName%_%stageName%
		domainUrl: 'https://meziviny.vojta.l/'
		domainUrlPdf: ''
		debuggerEditor: 'phpstorm://open?file=%file&line=%line'
		translations:
			insertNew: false
			markUsage: false

		dbalLog: false # vypis sql dotazu do souboru nettelog/mysql.log

		adminMail: '<EMAIL>'

		googleAnalyticsCode: "GTM-SUPERADMIN"
		googleAnalyticsName: "auto"

		mutations:
			cs:
				domain: meziviny.vojta.l
				urlPrefix: false
				mutationId: 1
				rootId: 1
				eshopRootId: 21
				hidePageId: 43
				systemPageId: 398
				internalName: česky
				robots: "noindex, nofollow"
				sitemap: true

	database:
		host: 127.0.0.1:3307
		database: 1691_meziviny_stage
		user: '1691_meziviny_st'
		password: 'FH11GlUxaEQu'
		profiler: FALSE
		lazy: true

services:
	nette.latteFactory:
		setup:
#			- setTempDirectory("../temp/cache/latte")
#			- setTempDirectory("") # odkomentované => vypnuti cache u latte

	cacheStorage:
		class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')


	mysql.panel: Dibi\Bridges\Tracy\Panel

	tracy.bar:
		setup:
			- @mysql.panel::register(@database)


session:
	expiration: 14 days
	cookie_secure: false

tracy:
#	maxDepth: 4
	bar:
		- Nette\Bridges\HttpTracy\SessionPanel

#application:
#	catchExceptions: true

