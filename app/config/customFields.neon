extensions:
	cf: SuperKoderi\CustomField\CustomFieldsExtension

cf:
	definitions:
		content:
			type: tinymce
			label: "Obsah"
		annotation:
			type: textarea
			label: "Anotace"
		text:
			type: text
			label: "Text"
		photo:
			type: image
			label: "Fotka"
		photos:
			type: image
			label: "Galerie"
			multiple: true
		file:
			type: file
			label: "Soubor"
		files:
			type: file
			label: "Soubory"
			multiple: true
		socialNetwork:
			type: group
			label: "socialNetwork"
			items:
				text:
					type: text
					label: "Text"
				url:
					type: text
					label: "URL"

		alertMessage:
			type: group
			label: "alertMessage"
			items:
				text:
					type: text
					label: "Text"
				url:
					type: text
					label: "URL"

		contactLink:
			type: group
			label: "contactLink"
			items:
				text:
					type: text
					label: "Text"
				url:
					type: text
					label: "URL"

		productsList:
			type: "group"
			label: "productsList"
			items:
				title:
					type: text
					label: "Nadpis"
				link:
					type: group
					label: "Odkaz"
					items:
						text:
							type: text
							label: "Text"
						url:
							type: suggest
							subType: seoLink
							label: "Link"
							placeholder: "Zadej SeoLink přes našeptá<PERSON>č"
							url: @cf.suggestUrls.searchSeoLink
				products:
					type: "list"
					label: "Vybrané produkty"
					items:
						product:
							label: "Produkt"
							type: suggest
							subType: product
							url: @cf.suggestUrls.searchProduct
		products:
					type: "group"
					label: "Produkty"
					items:
						products:
							type: "list"
							label: "Vybrané produkty"
							items:
								product:
									label: "Produkt"
									type: suggest
									subType: product
									url: @cf.suggestUrls.searchProduct
	fields:
		message:
			type: message
			label: "Tohle je needitovatelna zprava"
			variant: ok
		file:
			type: file
			label: "Soubor"
		file_multiple:
			type: file
			label: "Soubory"
			multiple: true
		name:
			type: text
			label: "Name"
			# defaultValue: "John Doe"
		image:
			type: group
			label: "Image"
			items:
				image:
					type: image
					label: "Image"
		image_multiple:
			type: image
			label: "Image multiple"
			multiple: true
		suggest:
			type: suggest
			subType: "tree"
			label: "Suggest"
			url: @cf.suggestUrls.searchMutationPage
		alias:
			type: suggest
			subType: alias
			label: "Suggest"
			url: @cf.suggestUrls.searchMutationAlias


		texts:
			type: group
			items:

				annotation:
					type: textarea
					label: "Anotace"
				content:
					type: tinymce
					label: "Obsah"

		description:
			type: textarea
			label: "Description"
			# placeholder: ""
		tinymce:
			type: tinymce
			label: "Tinymce"
		checkbox:
			type: checkbox
			label: "Checkbox"
		select:
			type: select
			label: "Select"
			options: [
				{ label: "Prvni", value: "first" },
				{ label: "Druha", value: "second" }
			]
		select_multiple:
			type: select
			label: "Select multiple"
			multiple: true
			options: [
				{ label: "Prvni", value: "first" },
				{ label: "Druha", value: "second" }
			]
		list:
			type: list
			label: "List 1"
			items:
				demopage:
					type: suggest
					subType: tree
					label: "Zadejte URL"
					url: @cf.suggestUrls.searchMutationPage
				firstName:
					type: text
					label: "Zadejte jméno"
				lastName:
					type: text
					label: "Zadejte jméno"
		group:
			type: group
			label: "Demo skupina"
			items:
				groupItem1:
					type: text
					label: "groupItem1"
					order: 2
				groupItem2:
					type: text
					label: "groupItem2"

		menu:
			type: list
			label: "Hlavní menu"
			items:
				page:
					type: suggest
					subType: tree
					label: "Stránka"
					placeholder: "Zadejte URL přes našeptávač"
					url: @cf.suggestUrls.searchMutationPage


		parameterForFilter:
			type: group
			label: "Nastavení filtru"
			items:
				visibleParameters:
					type: list
					label: Parametry
					items:
						parameter:
							type: suggest
							subType: parameter
							placeholder: Jméno parametru
							url: @cf.suggestUrls.searchParameterForFilter
						visibleCount:
							type: text
							label: "Počet viditelných hodnot"
						indexable:
							type: checkbox
							label: "Indexovatelné"
						numberAsRange:
							type: checkbox
							label: "Rozsah pro číselné hodnoty"


		feeds:
			type: group
			label: "Nastavení kategorií feedů"
			items:
				zbozi:
					type: text
					label: "Zboží"
				showItems:
					type: checkbox
					label: "Zobraz itemy"
					isGroupToggle: true
				heureka:
					type: text
					label: "Heureka"
				google:
					type: text
					label: "Google"


		userMenuLoggedUser:
			label: "User menu pro přihlášeného (hlavička)"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					placeholder: "Zadejte URL přes našeptávač"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		userMenuUnloggedUser:
			label: "User menu pro nepřihlášeného (hlavička)"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					placeholder: "Zadejte URL přes našeptávač"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		userSideMenu:
			label: "User side menu (uživatelská sekce)"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					placeholder: "Zadejte URL přes našeptávač"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		#meziviny

		blog_video_url:
			type: group
			label: "URL videa"
			items:
				url:
					type: text
					label: "URL"
					placeholder: 'Překopírujte url videa z adresního řádku služby YouTube'

		homepage_products_section_1:
			extends: @cf.definitions.productsList
			label: "Produkty na HP 1"

		product_recommended_meal_message:
			type: group
			label: "Doporučená kombinace k jídlu"
			items:
				text:
					type: textarea
					label: "Text"

		productFilter:
			type: group
			label: Nastavení pro katalog
			items:
				sortBoost:
					type: text
					label: "Boost pro 'TOP' řazení"

		system_messages:
			type: group
			label: "Systémová zpráva"
			items:
				text:
					type: textarea
					label: "Text"
				link:
					extends: @cf.definitions.contactLink
					label: "Odkaz"
				icon:
					type: select
					label: "Ikona"
					options: [
						{ label: "Glasses", value: "m-sm-glasses" },
						# { label: "Box", value: "m-box" },
						# { label: "Fork", value: "m-fork" },
						# { label: "Percentage", value: "m-percentage" }
					]

		shop_message:
			type: group
			label: "Systémová zpráva nad výpisem"
			items:
				heading:
					type: text
					label: "Nadpis"
				text:
					type: textarea
					label: "Obsah"
				link:
					type: group
					label: "Odkaz"
					items:
						text:
							type: text
							label: "Text"
						url:
							type: suggest
							subType: tree
							label: "Stránka"
							placeholder: "Zadejte URL přes našeptávač"
							url: @cf.suggestUrls.searchMutationPage
				icon:
					type: select
					label: "Ikona"
					options: [
						{ label: "Box", value: "m-box" },
						{ label: "Fork", value: "m-fork" },
						{ label: "Percentage", value: "m-percentage" }
					]
		pageContacts:
			type: group
			label: "Kontakty na konci stránky"
			items:
				title:
					type: text
					label: Nadpis
				phone:
					type: text
					label: "Telefon"
					inputType: tel
				email:
					type: text
					label: "E-mail"
					inputType: email

		pageCover:
			type: group
			label: "Fotka v záhlaví (1920x600)"
			items:
				image:
					type: image
					label: "Image"
		pageCoverShort:
			type: group
			label: "Fotka pro rozcestník (500x375)"
			items:
				image:
					type: image
					label: "Image"
		contact_page_text:
			type: group
			label: "Kontaktní adresa"
			items:
				text:
					type: textarea

		contact_page_map:
			extends: @cf.definitions.contactLink
			label: "Odkaz na mapu"

		contact_page_contacts:
			type: group
			label: "Kontakty"
			items:
				email:
					extends: @cf.definitions.contactLink
					label: "E-mail"
					inputType: email
				phoneGroup:
					type: group
					label: "Telefon"
					items:
						phoneText:
							type: text
							label: "Popis telefonu - dotazy"
						phone:
							extends: @cf.definitions.contactLink
							label: "Telefon - dotazy"
							inputType: tel
				phoneGroupOne:
					type: group
					label: "Telefon"
					items:
						phoneTextOne:
							type: text
							label: "Popis telefonu - rezervace"
						phoneOne:
							extends: @cf.definitions.contactLink
							label: "Telefon - rezervace"
							inputType: tel
				phoneGroupTwo:
					type: group
					label: "Telefon"
					items:
						phoneTextTwo:
							type: text
							label: "Popis telefonu - obchod"
						phoneTwo:
							extends: @cf.definitions.contactLink
							label: "Telefon - obchod"
							inputType: tel

		homepage_banner:
			type: list
			label: "Slideshow"
			items:
				imageDesktop:
					type: image
					label: "Obrázek pro desktop (1290x450)"
				imageMobil:
					type: image
					label: "Obrázek pro mobil (750x390)"
				page:
					type: suggest
					subType: tree
					label: "Stránka"
					placeholder: "Zadejte URL přes našeptávač"
					url: @cf.suggestUrls.searchMutationPage
				contentHeading:
					type: text
					label: "Nadpis"
				contentText:
					type: text
					label: "Text"

		homepage_benefits:
			type: list
			label: "Benefity"
			items:
				icon:
					type: image
					label: "Ikona"
				head:
					type: text
					label: "Nadpis"
				desc:
					type: text
					label: "Text"

		homepage_categories:
			type: list
			label: "Kategorie"
			items:
				icon:
					type: image
					label: "Hlavní ikona"
				page:
					type: suggest
					subType: tree
					label: "Stránka"
					placeholder: "Zadejte URL přes našeptávač"
					url: @cf.suggestUrls.searchMutationPage
				group:
					type: list
					label: "Položky"
					items:
						icon:
							type: image
							label: "Ikona"
						head:
							type: text
							label: "Nadpis"
						page:
							type: suggest
							subType: tree
							label: "Stránka"
							placeholder: "Zadejte URL přes našeptávač"
							url: @cf.suggestUrls.searchMutationPage

		homepage_ad:
			type: group
			label: "Reklama"
			items:
				image:
					type: image
					label: "Obrázek"
				url:
					type: text
					label: "URL"
					placeholder: 'Odkaz'

		category_banner:
			type: list
			label: "Slideshow"
			items:
				imageDesktop:
					type: image
					label: "Obrázek pro desktop (965x250)"
				imageMobil:
					type: image
					label: "Obrázek pro mobil (750x250)"
				url:
					type: text
					label: "URL"
				title:
					type: text
					label: "Alternativní text"

		category_banner_small:
			type: list
			label: "Slideshow ve sloupci filtrů"
			items:
				imageDesktop:
					type: image
					label: "Obrázek pro desktop (265x400)"
				url:
					type: text
					label: "URL"
				title:
					type: text
					label: "Alternativní text"
		menu_main:
			type: list
			label: "Hlavní menu"
			items:
				page:
					type: suggest
					subType: tree
					label: "Stránka"
					placeholder: "Zadejte URL přes našeptávač"
					url: @cf.suggestUrls.searchMutationPage

		eshopMenu:
			type: list
			label: "Eshop menu"
			items:
				group:
					type: group
					items:
						page:
							type: text
							label: "Text"
							placeholder: "Zadejte jméno kategorie"
						linkToggleRadio:
							type: group
							label: "Odkaz"
							hasContentToggle: true
							items:
								toggle:
									type: radio
									inline: true
									isContentToggle: true
									defaultValue: "seolink"
									options: [
										{ label: "SeoLink", value: "seolink"},
										{ label: "Stránka", value: "url" },
									]
								seolink:
									type: suggest
									subType: seoLink
									placeholder: "Zadejte SeoLink přes našeptávač"
									url: @cf.suggestUrls.searchSeoLink
								url:
									type: suggest
									subType: tree
									placeholder: "Zadejte název stránky přes našeptávač"
									url: @cf.suggestUrls.searchMutationPage

						subPages:
							type: list
							items:
								link:
									type: group
									label: "Subsekce"
									hasContentToggle: true
									items:
										toggle:
											type: radio
											inline: true
											isContentToggle: true
											defaultValue: "seolink"
											options: [
												{ label: "SeoLink", value: "seolink"},
												{ label: "Stránka", value: "url" },
											]
										seolink:
											type: suggest
											subType: seoLink
											placeholder: "Zadejte SeoLink přes našeptávač"
											url: @cf.suggestUrls.searchSeoLink
										url:
											type: suggest
											subType: tree
											placeholder: "Zadejte název stránky přes našeptávač"
											url: @cf.suggestUrls.searchMutationPage


		menu_social_networks:
			type: group
			label: "Sociální sítě"
			items:
				instagram:
					extends: @cf.definitions.socialNetwork
					label: "Instagram"
				facebook:
					extends: @cf.definitions.socialNetwork
					label: "Facebook"
				youtube:
					extends: @cf.definitions.socialNetwork
					label: "Youtube"

		menu_footer_1:
			type: group
			label: "Menu zápatí 1"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Položky menu"
					items:
						url:
							type: suggest
							subType: tree
							label: "Stránka"
							placeholder: "Zadejte URL přes našeptávač"
							url: @cf.suggestUrls.searchMutationPage

		menu_footer_2:
			type: group
			label: "Menu zápatí 2"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Položky menu"
					items:
						url:
							type: suggest
							subType: tree
							label: "Stránka"
							placeholder: "Zadejte URL přes našeptávač"
							url: @cf.suggestUrls.searchMutationPage

		cookies:
			label: "Cookies"
			type: "group"
			items:
				title:
					label: "Nadpis"
					type: text
					value: ""
				text:
					label: "Text"
					type: tinymce
					value: ""
				titleTwo:
					label: "Nadpis"
					type: text
					value: ""
				textTwo:
					label: "Text"
					type: tinymce
					value: ""
				btnSetPreferences:
					label: "Tlačítko - nastavit preference"
					type: text
					value: ""
				btnReject:
					label: "Tlačítko - Odmítnout"
					type: text
					value: ""
				btnConsentAndContinuation:
					label: "Tlačítko - souhlas a pokračování"
					type: text
					value: ""
				consentsTitle:
					label: "Nadpis - nastavení preferencí"
					type: text
					value: ""
				necessarilyLink:
					label: "Nezbytné - link"
					type: text
					value: ""
				necessarilyText:
					label: "Nezbytné - text"
					type: tinymce
					value: ""
				preferenceslLink:
					label: "Předvolby - link"
					type: text
					value: ""
				preferencesText:
					label: "Předvolby - text"
					type: tinymce
					value: ""
				analyticsLink:
					label: "Analytika - link"
					type: text
					value: ""
				analyticsText:
					label: "Analytika - text"
					type: tinymce
					value: ""
				marketingLink:
					label: "Marketingové - link"
					type: text
					value: ""
				marketingText:
					label: "Marketingové - text"
					type: tinymce
					value: ""
				btnConfirmSelected:
					label: "Tlačítko - potvrdit vybrané"
					type: text
					value: ""
				btnAcceptEverything:
					label: "Tlačítko - přijmout vše"
					type: text
					value: ""

		p_values_range:
			type: group
			label: "Rozsah hodnot"
			items:
				min:
					type: text
					inputType: number
					label: "Minimální hodnota"
				max:
					type: text
					inputType: number
					label: "Maximální hodnota"

		wines_iconic_wines:
			extends: @cf.definitions.products
			label: "Ikonická vína"

		topProducts:
			extends: @cf.definitions.products
			label: "Top produkty"

		viticultures:
			type: "group"
			label: "Vinařství"
			items:
				image:
					type: image
					label: "Logo"
				photo:
					type: image
					label: "Ilustrační foto"
				annotation:
					type: tinymce
					label: "Anotace"

	suggestUrls:
		searchVoucher:
			searchParameterName: search
			link: "/superadmin/search2/voucher"

		searchMutationPage:
			searchParameterName: search
			link: "/superadmin/search2/page-in-mutation"

		searchSeoLink:
			searchParameterName: search
			link: "/superadmin/search2/seo-link"
			params: []
		searchMutationTag:
			searchParameterName: search
			link: "/superadmin/search2/tag-in-mutation"
			params: []

		searchPage:
			searchParameterName: search
			link: "/superadmin/search2/page"
			params: []

		searchProduct:
			searchParameterName: search
			link: "/superadmin/search2/product"
			params: []

		searchProductTagParent:
			searchParameterName: search
			link: "/superadmin/search2/product-tag-parent"
			params: []

		searchSeoLinkParameterValues:
			searchParameterName: search
			link: "/superadmin/search2/seolink-parameter-values"
			params: []


		searchParameter:
			searchParameterName: search
			link: "/superadmin/search2/parameter"
			params: []

		searchParameterForFilter:
			searchParameterName: search
			link: "/superadmin/search2/parameter"
			params: [ 'types': ['select', 'multiselect', 'number'], 'onlyForFilter': 1]
		searchMutationBlogs:
			searchParameterName: search
			link: "/superadmin/search2/blog-in-mutation"
			params: []
		searchParameterValue:
			searchParameterName: search
			link: "/superadmin/search2/parameter-value"
			params: []
		searchVincultureParams:
			searchParameterName: search
			link: "/superadmin/search2/parameter-value"
			params: [ 'uids' : ["viticulture"]]
		searchVariants:
			searchParameterName: search
			link: "/superadmin/search2/variant"
			params: []
		searchMutationAlias:
			searchParameterName: search
			link: "/superadmin/search2/alias"
			params: []

	templates:
		#template: [customfields]

		# examples - todo to WIKI:
		# WIKI - https://www.notion.so/superkoders/Vlastn-pole-Custom-Fields-verze-1-0-2c3322c358224c769c0bdb1a9593b6d2
		#1) Page:default: [customfields] # CF pro stránku s šablonou Page:default
		#2) Product:detail: [customfields] # CF pro produkt
		#3) product-AAA: [customfields] # CF pro produkt jehož nejvyšším rodičem (hlavní katgorie) je UID produktové kategorie AAA
		#4) uid-XXX: [customfields] # CF pro stránku s uid = XXX
		#5) parameter-YYY: [customfields] #CF pro parametr s uid = YYY
		#6) parameters: [customfields] #CF pro všechny parametry obecně, parameter-YYY přebíjí
		#7) banner-ZZZ: [customfields] #CF pro banner s pozici = ZZZ
		#8) CF pro všechny stránky ve stromě, nasatvuje se do speciální sekce: customFieldsTemplates
		#9) user-ROLE || users

		mutation: [@cf.system_messages, @cf.eshopMenu, @cf.menu_main, @cf.menu_social_networks, @cf.menu_footer_1, @cf.menu_footer_2]

		Catalog:default: [@cf.parameterForFilter, @cf.topProducts, @cf.feeds, @cf.category_banner, @cf.category_banner_small]
		uid-search: [@cf.parameterForFilter]
		seoLink: [@cf.topProducts]
		seoLinkLocalization: [@cf.texts, @cf.category_banner, @cf.category_banner_small]
		uid-userSection: [@cf.userMenuUnloggedUser, @cf.userMenuLoggedUser, @cf.userSideMenu]

		# Page:default: [customFields]
		# Page:default: [@cf.file, @cf.file_multiple, @cf.name, @cf.image, @cf.image_multiple, @cf.suggest, @cf.description, @cf.tinymce, @cf.checkbox, @cf.select, @cf.select_multiple, @cf.list, @cf.parameterForFilter]

#			"parameter-color": [feeds]
#			"banner-hp": [event]
#			"banner-shopTop": [newProduct]
#			"banner-voucher": [voucher]
#			"banner-shopCatalog": [newProduct]
#			"banner-shopFilter": [newProduct]
#			"banner-blogDetail": [event]

#			"uid-eshop": [event]   -- pro tree s UID jde pridat specialni cf
#			"Product:detail": [name, image, image_multiple, suggest, description, tinymce, checkbox, select, select_multiple, list]
		# "Product:detail": [@cf.feeds, @cf.message, @cf.parameterForFilter, @cf.file, @cf.file_multiple, @cf.name, @cf.image, @cf.image_multiple, @cf.suggest, @cf.description, @cf.tinymce, @cf.checkbox, @cf.select, @cf.select_multiple, @cf.group, @cf.list]
#			"product-eshop-1": [article]
#			banner-shopTop: [name]
#			parameter-text: [name]
		"Page:contact": [@cf.pageCover, @cf.pageCoverShort]
		"Page:default": [@cf.pageCover, @cf.pageCoverShort, @cf.pageContacts]
		"Page:viticultures": [@cf.pageCover, @cf.pageCoverShort]
		"Page:tastingGuide": [@cf.pageCover, @cf.pageCoverShort]
		"Blog:default": [@cf.pageCover, @cf.pageCoverShort]
		"Blog:main": [@cf.pageCover, @cf.pageCoverShort]
		"Blog:detail": [@cf.blog_video_url]
		"Homepage:default": [@cf.homepage_banner, @cf.homepage_benefits, @cf.homepage_categories, @cf.homepage_ad, @cf.homepage_products_section_1]
		"Product:detail": [@cf.product_recommended_meal_message, @cf.productFilter]
		"uid-contact": [@cf.contact_page_text, @cf.contact_page_map, @cf.contact_page_contacts]
		"uid-eshop": [@cf.shop_message]
		"parameter-alcohol": [@cf.p_values_range]
		"parameter-extract": [@cf.p_values_range]
		"parameter-acidity": [@cf.p_values_range]
		"parameter-residual-sugar": [@cf.p_values_range]
		"uid-iconicwines": [@cf.wines_iconic_wines]
		"uid-cookie": [@cf.cookies]
		parameterValue-awards: [@cf.image]
		parameterValue-viticulture: [@cf.viticultures]
