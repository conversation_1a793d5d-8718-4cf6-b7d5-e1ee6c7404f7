extensions:
	cc: <PERSON><PERSON><PERSON><PERSON>\CustomContent\CustomContentExtension

cc:
	definitions:
		text:
			type: group
			label: "Text"
			items:
				text: @cf.definitions.text
		content:
			type: group
			label: "Obsah"
			items:
				content: @cf.definitions.content
		gallery:
			type: group
			label: "Galerie"
			items:
				content: @cf.definitions.photos
		modalList:
			type: group
			label: "Modal list"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				text:
					type: textarea
					label: "Text"
					translatable: true

	components:
		textButton:
			icon: "bacon"
			template: "cleanText"
			category: "Category 1"
			hotkey: true
			definition: @cc.definitions.text

		contentButton:
			icon: "angry"
			template: "content"
			category: "Category 2"
			definition: @cc.definitions.content

		galleryButton:
			icon: "image"
			template: "gallery"
			category: "Category 3"
			definition: @cc.definitions.gallery

		textButton2:
			icon: "bacon"
			template: "cleanText"
			category: "Category 3"
			definition: @cc.definitions.text

		contentButton2:
			icon: "angry"
			template: "content"
			category: "Category 1"
			definition: @cc.definitions.content

		galleryButton2:
			icon: "image"
			template: "gallery"
			category: "Category 2"
			definition: @cc.definitions.gallery

		textButton3:
			icon: "bacon"
			template: "cleanText"
			category: "Category 2"
			definition: @cc.definitions.text

		contentButton3:
			icon: "angry"
			template: "content"
			category: "Category 1"
			definition: @cc.definitions.content

		galleryButton3:
			icon: "image"
			template: "gallery"
			category: "Category 3"
			hotkey: true
			definition: @cc.definitions.gallery

		textButton4:
			icon: "bacon"
			template: "cleanText"
			category: "Category 1"
			definition: @cc.definitions.text

		contentButton4:
			icon: "angry"
			template: "content"
			category: "Category 2"
			hotkey: true
			definition: @cc.definitions.content

		galleryButton4:
			icon: "image"
			template: "gallery"
			category: "Category 3"
			definition: @cc.definitions.gallery

		content:
			icon: "angry"
			template: "content"
			category: "Text"
			hotkey: true
			definition: @cc.definitions.content

		modalList:
			icon: "user-alt"
			template: "modales"
			definition: @cc.definitions.modalList
			category: "Obsah"

	templates:
		"Blog:detail": [
			@cc.textButton,
			@cc.contentButton,
			@cc.galleryButton,
			@cc.textButton2,
			@cc.contentButton2,
			@cc.galleryButton2,
			@cc.textButton3,
			@cc.contentButton3,
			@cc.galleryButton3,
			@cc.textButton4,
			@cc.contentButton4,
			@cc.galleryButton4,
		]

services:
	-
		implement: SuperKoderi\Components\ICustomContentRendererFactory
		inject: true
