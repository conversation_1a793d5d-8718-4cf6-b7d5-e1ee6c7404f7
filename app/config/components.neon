	services:
		#FORM/COMPONENT FACTORIES
		- App\Components\VisualPaginator\VisualPaginatorFactory

		-
		    implement: App\Admin\Components\ISignInFormFactory
		    inject: true

		-
		    implement: App\Admin\Components\ILostPasswordFormFactory
		    inject: true

		-
		    implement: App\Admin\Components\IResetPasswordFormFactory
		    inject: true

		-
		    implement: \SuperKoderi\Components\IContactFormFactory
		    inject: true
		-
		    implement: \SuperKoderi\Components\ISynonymsFormFactory
		    inject: true

		-
			implement: \SuperKoderi\Components\IServicesFormFactory
		-
		    implement: \SuperKoderi\Components\ISendOnEmailFormFactory
		    inject: true
		-
			implement: \SuperKoderi\Components\INewsletterFormFactory
			inject: true

		-
			implement: SuperKoderi\Components\IRedirectMultiFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\ISignInFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IProfileFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IChangePasswordFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IRegistrationFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\ILostPasswordFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IOrderHistoryFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IOrderHistoryDetailFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IMessageForFormFactory
		-
		    implement: \SuperKoderi\Components\IUserEditFormFactory
		    inject: true
		-
			implement: \SuperKoderi\Components\ILangSwitcherFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\ICanonicalUrlFactory
			inject: true

		-
			implement: \SuperKoderi\Components\IOrderBasketFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IOrderStep1FormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IOrderStep2FormFactory
			inject: true

		-
			implement: \SuperKoderi\Components\IMenuFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IToggleLanguageFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IRobotsFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IUserMenuFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IUserSideMenuFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IGroupFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IRedirectFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IFilterFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IEntityListFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IPrebasketFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IProductDeliveryInfoFactory
			inject: true
		-
			implement: SuperKoderi\Components\IBreadcrumbFactory
			inject: true
		-
			implement: SuperKoderi\Components\IProductParametersFactory
			inject: true
		-
			implement: SuperKoderi\Components\IVariantPickerFactory
			inject: true
		-
			implement: SuperKoderi\Admin\Components\IProductFormFactory
			inject: true
		-
			implement: \SuperKoderi\Components\IFavoriteProductsFactory
			inject: true
		-
			implement: SuperKoderi\Components\ParameterValueFormFactory
			inject: true
		-
			implement: App\FrontModule\Components\Modal\ModalFactory
			inject: true

		- SuperKoderi\Admin\Components\ProductFormBuilder
		- SuperKoderi\Admin\Components\ProductFormSuccess
		- App\AdminModule\Components\ProductDataGrid\ProductDataGridFactory
		- App\AdminModule\Components\ProductShellForm\ProductShellFormFactory


