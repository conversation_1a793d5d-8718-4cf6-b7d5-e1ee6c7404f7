parameters:
	config:
		erp:
			connectors:
				infosConnector: []

			products:
				limit: 200 #ERP API limit parameter
				publicNew: false #if true, publish new products
				updateInternalValues: false #if false, internal names are set only when creating a new product

services:
	#connectors - must be named, there can be more than one connectors of the same class (with different config)
	erp.infosConnector: App\Model\Erp\Connector\InfosErpConnector('infosConnector')

	#importers - must be named, there can be more than one importers of the same class (with different connectors)
	erp.ProductImporter: App\Model\Erp\Importer\InfosProductImporter(@erp.infosConnector)

	#exporters - must be named, there can be more than one exporters of the same class (with different connectors)
#	erp.OrderExporter: App\Model\Erp\Exporter\AlOrderExporter(@erp.infosConnector)

	#import processors - must be named, there can be more than one processors of the same interface
	erp.ProductImportProcessor: App\Model\Erp\ImportProcessor\InfosProductImportProcessor


monolog:
	channel:
		erpInfo:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/info.log, 15, Monolog\Logger::INFO)
		erpError:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/erp/error.log, 15, Monolog\Logger::ERROR)
