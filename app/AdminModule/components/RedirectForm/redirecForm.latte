{form form}
	<div class="grid-row">

		{foreach $flashes as $flash}
			<div class="message message-{$flash->type}">{$flash->message}</div>
		{/foreach}

		<p class="grid-1-3 {if $form['oldUrl']->hasErrors()}error{/if}">
			{label oldUrl /}
			<span class="inp-fix">
                <input n:name="oldUrl" class="inp-text {if $form['oldUrl']->hasErrors()}error{/if}"/>
            </span>
		</p>

		<p class="grid-1-3  {if $form['newUrl']->hasErrors()}error{/if}">
			{label newUrl /}
			<span class="inp-fix">
                <input n:name="newUrl" class="inp-text  {if $form['newUrl']->hasErrors()}error{/if}"/>
            </span>
		</p>

		<p class="grid-1-3">
			{label code /}
			<span class="inp-fix">
                {input code class => 'inp-text'}
            </span>
		</p>

		<div class="grid-row">
			<p class="grid-1-6">
				<button class="btn btn-green btn-icon-before">
                    <span>
                        <span class="icon icon-checkmark"></span>
                        {_save_button}
                    </span>
				</button>
				{if $redirect && $redirect->isPersisted()}
					<a class="btn btn-red btn-icon-before btn-delete" n:href="delete">
                        <span>
                            <span class="icon icon-close"></span>
                            {_delete_button}
                        </span>
					</a>
				{/if}
			</p>

		</div>

	</div>
{/form}
