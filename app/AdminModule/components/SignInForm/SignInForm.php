<?php declare(strict_types = 1);

namespace App\Admin\Components;

use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\Translator;

/**
 * @property-read DefaultTemplate $template
 */
class SignInForm extends UI\Control
{

	use hasMessageForFormComponentTrait;

	public function __construct(
		private Translator $translator,
	) {}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/signInForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);
		$form->addText('username', 'username')
			->setRequired('msg_error_username');

		$form->addPassword('password', 'password')
			->setRequired('msg_error_password');

		$form->addCheckbox('remember', 'remember');

		$form->addSubmit('send', 'login_button');

		$form->onError[] = function ($form) {
			foreach ($form->errors as $error) $form->getPresenter()->flashMessage($error, 'error');
		};

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'signInFormSucceeded'];
		return $form;
	}


	public function signInFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			$user = $this->presenter->getUser();
			$user->login($values->username, $values->password);

			if ($values->remember) {
				$user->setExpiration('14 days', false);
			} else {
				$user->setExpiration('2 days', true);
			}

			$this->presenter->redirect('Page:');

		} catch (AuthenticationException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}
	}

}


interface ISignInFormFactory
{

	function create(): SignInForm;

}
