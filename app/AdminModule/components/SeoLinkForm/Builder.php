<?php

declare(strict_types=1);

namespace App\Admin\Components\SeoLinkForm;

use App\Model\ProductLocalization;
use App\Model\SeoLink;
use App\Model\SeoLinkLocalization;
use App\PostType\Core\AdminModule\Components\Form\Builder as CoreBuilder;
use Nette\Forms\Container;

final class Builder
{

	public function __construct(
		private readonly CoreBuilder $coreBuilder,
	)
	{
	}

	public function build(\Nette\Application\UI\Form $form, SeoLinkLocalization $seoLinkLocalization, array $postData): void
	{
		$this->coreBuilder->addLocalization($form, $seoLinkLocalization);
		$this->addCategoriesToContainer($form, $seoLinkLocalization, $postData);
		$this->coreBuilder->addRoutable($form, $seoLinkLocalization);
		$this->coreBuilder->addParent($form, $seoLinkLocalization->getParent());

		$this->addParameterValuesToForm($form, $seoLinkLocalization->seoLink, $postData);

		$this->coreBuilder->addButtons($form);
	}

	private function addParameterValuesToForm(\Nette\Application\UI\Form $form, SeoLink $seoLink, array $postData): void
	{
		$parameterValuesContainer = $form->addContainer('parameterValues');
		if (isset($postData['parameterValues'])) {
			foreach ($postData['parameterValues'] as $parameterValueKey => $parameterValue) {
				$parameterValueContainer = $parameterValuesContainer->addContainer($parameterValueKey);
				$parameterValueContainer->addHidden('id');
				$parameterValueContainer->addText('name', 'name');
			}
		} else {
			foreach ($seoLink->parameterValues as $parameterValueKey => $parameterValue) {
				$parameterValueContainer = $parameterValuesContainer->addContainer($parameterValueKey);
				$parameterValueContainer->addHidden('id', $parameterValue->id);
				$parameterValueContainer->addText('name', 'name')->setDefaultValue($parameterValue->internalValue);
			}
		}

		// fake
		$parameterValueContainer = $parameterValuesContainer->addContainer('newItemMarker');
		$parameterValueContainer->addHidden('id');
		$parameterValueContainer->addText('name', 'name');
	}

	private function addCategoriesToContainer(Container $localizationContainer, SeoLinkLocalization $localization, array $postData): void
	{
		$seoLinkLocalizationContainer = $localizationContainer->addContainer('seoLinkLocalization')->addContainer('categories');
		if ($postData) {
			if (isset($postData['seoLinkLocalization']['categories'])) {
				foreach ($postData['seoLinkLocalization']['categories'] as $categoryKey=>$category) {
					$categoryContainer = $seoLinkLocalizationContainer->addContainer($categoryKey);
					$categoryContainer->addHidden('id');
					$categoryContainer->addText('name', 'name');
				}
			}
		} else {

			$categories = [];
			if ($localization->category !== null) {
				$categories[] = $localization->category;
			}
			foreach ($categories as $categoryKey=>$category) {
				$categoryContainer = $seoLinkLocalizationContainer->addContainer($categoryKey);
				$categoryContainer->addHidden('id', $category->id);
				$categoryContainer->addText('name', 'name')
					->setDefaultValue($category->getPathSentenceWithMyself());

			}
		}

		//fake
		$categoryContainer = $seoLinkLocalizationContainer->addContainer('newItemMarker');
		$categoryContainer->addHidden('id');
		$categoryContainer->addText('name', 'name');
	}

}
