<?php declare(strict_types = 1);

namespace AdminModule;

use App\Admin\Components\ILostPasswordFormFactory;
use App\Admin\Components\IResetPasswordFormFactory;
use App\Admin\Components\ISignInFormFactory;
use App\Admin\Components\LostPasswordForm;
use App\Admin\Components\ResetPasswordForm;
use App\Admin\Components\SignInForm;
use App\Model\UserHashModel;
use Nette\Application\Attributes\Persistent;
use Nette\DI\Attributes\Inject;

/**
 * Sign in/out presenters.
 */
class SignPresenter extends BasePresenter
{

	#[Persistent]
	public string $backlink = '';

	#[Inject]
	public UserHashModel $userHashModel;

	#[Inject]
	public ILostPasswordFormFactory $lostPasswordFormFactory;

	#[Inject]
	public ISignInFormFactory $signInFormFactory;

	#[Inject]
	public IResetPasswordFormFactory $restPasswordFormFactory;

	public function actionOut(): void
	{
		$this->getUser()->logout(true);
		$this->flashMessage('msg_info_logout');
		$this->redirect('default');
	}


	public function actionLostPassword(): void
	{
		if ($this->user->loggedIn) {
			$this->redirect('Page:default');
		}
	}


	public function actionResetPassword(?string $hashToken): void
	{
		if ($hashToken) {
			$userHash = $this->orm->userHash->getBy(['hash' => $hashToken]);
			if ($userHash && $userHash->isValid()) {

			} else {
//				$this->flashMessage("reset_password_expired_link", "error");
				$this->getComponent('lostPasswordForm')->flashMessage('reset_password_expired_link', 'error');
				$this->redirect('Sign:lostPassword');
			}
		} else {
//			$this->flashMessage("reset_password_no_valid_link", "error");
			$this->getComponent('lostPasswordForm')->flashMessage('reset_password_no_valid_link', 'error');
			$this->redirect('Sign:lostPassword');
		}
	}


	public function createComponentLostPasswordForm(): LostPasswordForm
	{
		return $this->lostPasswordFormFactory->create($this->orm->mutation->getByCode('cs'));
	}

	public function createComponentResetPasswordForm(): ResetPasswordForm
	{
		$hash = $this->getParameter('hashToken');
		return $this->restPasswordFormFactory->create($hash);
	}


	protected function createComponentSignInForm(): SignInForm
	{
		return $this->signInFormFactory->create();
	}

}
