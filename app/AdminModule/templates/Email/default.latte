{templateType AdminModule\EmailPresenterTemplate}
{varType App\Model\EmailTemplate[] $emailTemplates}

{block content}
<div class="box-title">
	<p class="r" n:if="$isDeveloper">
		<a n:href="Email:add" class="btn">
			<span><span class="icon"></span>{translate}New template{/translate}</span>
		</a>
	</p>
	<h1 n:block=title>{translate}E-mails template{/translate}</h1>
</div>
{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
{/snippet}
{form filterForm class => "form-filter"}
	<div class="grid-row">
		<p class="grid-1-4">
			{label fulltext /} <span class="grey">({_label_internalName}, {_label_subject})</span>
			<span class="inp-fix">
				{input fulltext class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-5">
			{label mutation /}
			<span class="inp-fix inp-fix-select">
				{input mutation class => 'inp-text'}
			</span>
		</p>
		{*
		<div class="grid-1-4">
			<div class="grid-row">
				<p class="grid-1-2 no-label">
					<span class="inp-item inp-center">
						{input isDefault:}
						{label isDefault: /}
					</span>
				</p>
			</div>
		</div>
		*}
		<p class="grid-1-4 right">
			<br/>
			<button class="btn btn-green btn-icon-before">
				<span><span class="icon icon-checkmark"></span> {_filter_button}</span>
			</button>
			<a n:href="clearFilter!" class="btn btn-red btn-icon-before" n:if="$filterSession->data">
				<span><span class="icon icon-close"></span> {_filter_cancel_button}</span>
			</a>
		</p>
	</div>
{/form}

<table>
	<thead>
	<tr>
		<th>{_label_internalName}</th>
		<th>{_label_mutation}</th>
		<th>{_label_subject}</th>
		{if $isDeveloper}
			<th>{_label_key}</th>
			<th>{_onlyForDevelopers}</th>
		{/if}
		<th></th>
	</tr>
	</thead>
	<tbody>
	{varType App\Model\EmailTemplate $emailTemplate}
	{foreach $emailTemplates as $emailTemplate}

		<tr class="clickable">
			<td>{$emailTemplate->name}</td>
			<td>{$emailTemplate->mutation->name}</td>
			<td>{$emailTemplate->subject}</td>
		{if $isDeveloper}
			<td n:if="$isDeveloper">{$emailTemplate->key}</td>
			<td n:class="$emailTemplate->isDeveloper ? green, red">{if $emailTemplate->isDeveloper}yes{else}no{/if}</td>
		{/if}
			<td><a n:href="edit $emailTemplate->id" class="icon icon-pencil"><span class="vhide">{_"edit"}</span></a>
			</td>
		</tr>
	{/foreach}
	</tbody>
</table>

<div class="paging">
	<p class="l">
		{control pager:admin, showPages => "true"}
	</p>
	<p class="r">
		{control pager:admin, class => "r"}
	</p>
</div>
