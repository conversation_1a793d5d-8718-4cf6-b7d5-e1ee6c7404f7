{block #content}

<h1>{_"Elastic index log"}
	({$env})
	<a n:href="this" class="small">
		Obnovit
	</a>
</h1>


{foreach $flashes as $flash}
	<div class="message message-{$flash->type}">{$flash->message}</div>
{/foreach}

<h3>Aktuální indexy ({count($activeIndexes)})</h3>
<table>
	<th>ID</th>
	<th><PERSON><PERSON><PERSON></th>
	<th>Typ</th>
	<th>Index</th>
	<th>Počet položek</th>
	<th>Počet chyb</th>
	<th>Poslední ID chyby</th>
	<th>Velikost v Bytech</th>
	<th>Začátek</th>
	<th>Konec</th>
	<th>{_"Naplnit"}</th>

	{foreach $activeIndexes as $activeIndex}
		<tr style="font-family: Courier">
			<td>{$activeIndex->entity->id}</td>
			<td>{$activeIndex->entity->mutation->langCode}</td>
			<td>{$activeIndex->entity->type}</td>
			<td>{$activeIndex->entity->name}</td>
			<td>
				{if $activeIndex->stats}
					{$activeIndex->stats["docs"]["count"]}
				{/if}
			</td>
			<td>
				{$activeIndex->entity->errorCount}
			</td>
			<td>
				{foreach $activeIndex->entity->errorDetailItems as $errorDetailItem}
					{$errorDetailItem->class} - {$errorDetailItem->id} <br>
					{$errorDetailItem->msg}
				{/foreach}

			</td>
			<td>
				{if $activeIndex->stats}
					{$activeIndex->stats["store"]["size_in_bytes"]} B
				{/if}
			</td>
			<td>
				{if $activeIndex->entity->startTime}
					{$activeIndex->entity->startTime->format('Y-m-d H:i:s')}
				{/if}
			</td>
			<td>
				{if $activeIndex->entity->finishTime}
					{$activeIndex->entity->finishTime->format('Y-m-d H:i:s')}
				{/if}
			</td>
			<td>
				<a n:href="fill!, id=>$activeIndex->entity->id" class=""
						data-controller="Confirm"
						data-confirm-message-value="Opravdu si přejete znovu naplnit index?"
						data-action="Confirm#confirm">
					Znovu naplnit
				</a>
				<br>
				<a n:href="fill!, id=>$activeIndex->entity->id, limit=>5" class="grey"
						data-controller="Confirm"
						data-confirm-message-value="Opravdu si přejete znovu naplnit index?"
						data-action="Confirm#confirm">
					Znovu naplnit demo daty
				</a>
			</td>

		</tr>
	{/foreach}
</table>

{if count($oldIndexes)}
	<h3>Neaktivní indexy</h3>
	<table>
		<th>ID</th>
		<th>Mutace</th>
		<th>Typ</th>
		<th>Index</th>
		<th>Počet položek</th>
		<th>Počet chyb</th>
		<th>Poslední ID chyby</th>
		<th>Velikost v Bytech</th>
		<th>Začátek</th>
		<th>Konec</th>
		<th>{_"Smazat z ES"}</th>
		<th>{_"Naplnit"}</th>
		<th>{_"Aktivovat"}</th>
		{foreach $oldIndexes as $oldIndex}
			<tr style="font-family: Courier">
				<td>{$oldIndex->entity->id}</td>
				<td>{$oldIndex->entity->mutation->langCode}</td>
				<td>{$oldIndex->entity->type}</td>
				<td>{$oldIndex->entity->name}</td>
				<td>
					{if $oldIndex->stats}
						{$oldIndex->stats["docs"]["count"]}
					{/if}
				</td>
				<td>
					{$oldIndex->entity->errorCount}
				</td>
				<td>
					{foreach $oldIndex->entity->errorDetailItems as $errorDetailItem}
						{$errorDetailItem->class} - {$errorDetailItem->id} <br>
						{$errorDetailItem->msg}
					{/foreach}
				</td>
				<td>
					{if $oldIndex->stats}
						{$oldIndex->stats["store"]["size_in_bytes"]} B
					{/if}
				</td>
				<td>
					{if $oldIndex->entity->startTime}
						{$oldIndex->entity->startTime->format('Y-m-d H:i:s')}
					{/if}
				</td>
				<td>
					{if $oldIndex->entity->finishTime}
						{$oldIndex->entity->finishTime->format('Y-m-d H:i:s')}
					{/if}
				</td>
				<td>
					<a n:href="deleteIndex!, id=>$oldIndex->entity->id" class="red"
						data-controller="Confirm"
						data-confirm-message-value="Opravdu si přejete smazat index?"
						data-action="Confirm#confirm">
						Smazat
					</a>
				</td>
				<td>
					<a n:href="fill!, id=>$oldIndex->entity->id" class=""
						data-controller="Confirm"
						data-confirm-message-value="Opravdu si přejete znovu naplnit index?"
						data-action="Confirm#confirm">
						Znovu naplnit
					</a>
					<br>
					<a n:href="fill!, id=>$oldIndex->entity->id, limit=>5" class="grey"
						  data-controller="Confirm"
						  data-confirm-message-value="Opravdu si přejete znovu naplnit index?"
						  data-action="Confirm#confirm">
						Znovu naplnit demo daty
					</a>
				</td>
				<td>
					{if $oldIndex->entity->active !== 1}
						<a n:href="markAsActive!, id=>$oldIndex->entity->id" class="green"
							data-controller="Confirm"
							data-confirm-message-value="Opravdu si přejete aktivovat index?"
							data-action="Confirm#confirm">Aktivovat
						</a>
					{/if}
				</td>
			</tr>
		{/foreach}
	</table>
{/if}


{if count($unusedIndexes)}
	<h3>Odpojené indexy</h3>
	<table>
		<th>index</th>
		<th>Velikost v Bytech</th>
		{foreach $unusedIndexes as $unusedIndex}
			<tr style="font-family: Courier">
				<td>{$unusedIndex['index']}</td>
				<td>{$unusedIndex['store.size']}</td>
			</tr>
		{/foreach}
	</table>
{/if}


