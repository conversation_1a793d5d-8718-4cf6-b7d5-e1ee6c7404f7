<div class="grid-row">
	<p class="grid-1-2">
		{label alias /}<br />
		<a id="regeneratealias" class="btn r" data-action="{link Page:regenerateAlias}" data-id="{if isset($object) && $object->isPersisted()}{$object->id}{/if}" data-lg="cs">
			<span>{_from_name}</span>
		</a>
		<span class="inp-fix">
			{input alias class => 'inp-text w-full alias'}
		</span>
	</p><br />

	<p class="grid-1-2">
		{label nameTitle /}<br>
		<span class="inp-fix">
			{input nameTitle class => 'inp-text w-full'}
		</span>
	</p>
	<p class="grid-1-2">
		{label nameAnchor /}<br>
		<span class="inp-fix">
			{input nameAnchor class => 'inp-text w-full'}
		</span>
	</p>


	<p class="grid-1-2">
		{label description /}<br>
		<span class="inp-fix">
			{input description class => 'inp-text w-full'}
		</span>
	</p>
	<p class="grid-1-2">
		{label aliasHistoryString /}<br>
		<span class="inp-fix">
			{input aliasHistoryString class => 'inp-text w-full'}
		</span>
	</p>
	<p class="grid-1">
		{label keywords /}<br>
		<span class="inp-fix">
			{input keywords class => 'inp-text w-full'}
		</span>
	</p>
</div>
