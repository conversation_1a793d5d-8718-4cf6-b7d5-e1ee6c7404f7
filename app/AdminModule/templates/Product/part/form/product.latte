{if !isset($k)} {var $k='XXX'} {/if}
{if !isset($productName)} {var $productName=''} {/if}
{if !isset($productInfo)} {var $productInfo=NULL} {/if}
{if !isset($key)} {var $key='products'} {/if}
<li>
	<div class="inner">
		<span class="drag-area js-handle"></span>
		<span class="inp-fix inp-fix-suggest">
			{*
			<select name="{$key}[{$k}]" id="inp-product{$k}" data-val="" class="inp-text w-full">
				<option n:foreach="$products as $pk => $p"  value="{$p->id}" {if $productName == $p->id}selected{/if}>
					{$p->name|prepareStrJs} {if !$p->public}(nepublikovaný){/if}
				</option>
			</select>
			*}
			<input type="text" name="q" class="inp-text inp-suggest"
			       data-suggest="/{$config['adminAlias']}/search/"
			       data-parent="{if isset($object->id)}{$object->id}{/if}"
			       value="{if isset($productInfo)}{$productInfo->name}{if !$productInfo->public} (nepublikovaný){/if}{/if}"/>
			<input type="hidden" name="{$key}[{$k}]" value="{$productName}" class="suggest-value"/>
		</span>
		<input type="hidden" name="{$key}Sort[{$k}]" value="{$k}" class="inp-sort" />
		<a href="#" class="icon icon-close remove"></a>
	</div>
</li>
