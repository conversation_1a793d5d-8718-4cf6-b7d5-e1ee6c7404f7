{block #content}
{varType App\Model\Order $object}

{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
{/snippet}
<div class="box-title">
	<p class="r">
		<a n:href="default" class="btn btn-icon-before">
			<span><span class="icon icon-arrow-left "></span> {_"Back"}</span>
		</a>
	</p>
	<h1>{_'Order'} {_'no.'} {$object->number}{if $object->isDeleted} ({_'order_deleted'}){/if}</h1>
</div>

<div class="grid-row">
	<div class="grid-1-3">
		<p>
			<span class="icon icon-globe"></span>
			<span class=""><strong>{$object->mutation->name}</strong></span><br>
			{_'Order date'}:&nbsp;<strong>{$object->created|date:'%d. %m. %Y %H:%M'}</strong><br />
			{_'Order state'}:&nbsp;<strong>{_'order_status_' . $object->status}</strong><br />
			{_'Order payment'}:&nbsp;<strong>{_$object->paymentName}</strong><br />

			{if $object->paymentType == \App\Model\Order::PAYMENT_ONLINE}
				{_'Order payment status'}: &nbsp; {if $object->paymentStatus == \App\Model\Order::ONLINE_PAYMENT_PAID}
					<span class="green"><strong>{_'orderPaid'}</strong></span>
				{else}
					{translate}card_{$object->paymentStatus}{/translate}
				{/if}
				<br/>
			{/if}
		</p>
		<br/>
	</div>
	<div class="grid-1-3">
		<h3>{_'order_title_email_sent'}:</h3>
		<p>
			{if $object->mailConfirmationSend }{_'order_title_email_confirmation'}: <strong>{$object->mailConfirmationSend|date:'%d. %m. %Y %H:%M'}</strong><br>{/if}
			{if $object->mailProgress }{_'order_title_email_progress'}: <strong>{$object->mailProgress|date:'%d. %m. %Y %H:%M'}</strong><br>{/if}
			{if $object->mailSent }{_'order_title_email_shipped'}: <strong>{$object->mailSent|date:'%d. %m. %Y %H:%M'}</strong><br>{/if}
			{if $object->mailStorno }{_'order_title_email_cancel'}: <strong>{$object->mailStorno|date:'%d. %m. %Y %H:%M'}</strong><br>{/if}
		</p>
	</div>
</div>

{form editForm}
<div class="grid-row">
	<p class="grid-1-3">

		{label status /}<br>
		<button class="btn btn-green btn-icon-before r">
			<span><span class="icon icon-checkmark"></span> {_change_status_button}</span>
		</button>

		<span class="inp-fix  inp-fix-select">
			{input status class => 'inp-text w-full'}
		</span>
	</p>
	{*
	<p class="grid-1-3">
		{label barCode /}
		<span class="inp-fix">
			{input barCode class => 'inp-text'}
		</span>
	</p>
	 *}
</div>
<br>
<div class="grid-row boc-detail-table">
	<div class="grid-1-3">
		<h2>{_'Billing address'}</h2>

		<div class="grid-row">
			<p class="grid-1-2">
				{label firstname /}
				<span class="inp-fix">
					{input firstname class => 'inp-text'}
				</span>
			</p>
			<p class="grid-1-2">
				{label lastname /}
				<span class="inp-fix">
					{input lastname class => 'inp-text'}
				</span>
			</p>
			<p class="grid-1-2">
				{label email /}
				<span class="inp-fix">
					{input email class => 'inp-text'}
				</span>
			</p>
			<p class="grid-1-2">
				{label phone /}
				<span class="inp-fix">
					{input phone class => 'inp-text'}
				</span>
			</p>

			<p class="grid-1">
				{label street /}
				<span class="inp-fix">
					{input street class => 'inp-text'}
				</span>
			</p>

			<p class="grid-1-2">
				{label city /}
				<span class="inp-fix">
					{input city class => 'inp-text'}
				</span>
			</p>

			<p class="grid-1-2">
				{label zip /}
				<span class="inp-fix">
					{input zip class => 'inp-text'}
				</span>
			</p>

			<p class="grid-1">
				{label state /}
				<span class="inp-fix">
					{input state class => 'inp-text'}
				</span>
			</p>

			<p class="grid-1">
				{label infotext /}
				<span class="inp-fix">
					{input infotext class => 'inp-text'}
				</span>
			</p>
		</div>

	</div>
	<div class="grid-1-3">
		<h2>{_'Corporate information'}</h2>

		<div class="grid-row">
			<p class="grid-1">
				{label company /}
				<span class="inp-fix">
					{input company class => 'inp-text'}
				</span>
			</p>

			<p class="grid-1">
				{label ic /}
				<span class="inp-fix">
					{input ic class => 'inp-text'}
				</span>
			</p>

			<p class="grid-1">
				{label dic /}
				<span class="inp-fix">
					{input dic class => 'inp-text'}
				</span>
			</p>
		</div>

	</div>
	<div class="grid-1-3">
		<h2>{_'Delivery address'}</h2>

		<div class="grid-row">

			<p class="grid-1-2">
				{label dFirstname /}
				<span class="inp-fix">
					{input dFirstname class => 'inp-text'}
				</span>
			</p>
			<p class="grid-1-2">
				{label dLastname /}
				<span class="inp-fix">
					{input dLastname class => 'inp-text'}
				</span>
			</p>

			<p class="grid-1-2">
				{label dCompany /}
				<span class="inp-fix">
					{input dCompany class => 'inp-text'}
				</span>
			</p>
			<p class="grid-1-2">
				{label dPhone /}
				<span class="inp-fix">
					{input dPhone class => 'inp-text'}
				</span>
			</p>

			<p class="grid-1">
				{label dStreet /}
				<span class="inp-fix">
					{input dStreet class => 'inp-text'}
				</span>
			</p>
			<p class="grid-1-2">
				{label dCity /}
				<span class="inp-fix">
					{input dCity class => 'inp-text'}
				</span>
			</p>
			<p class="grid-1-2">
				{label dZip /}
				<span class="inp-fix">
					{input dZip class => 'inp-text'}
				</span>
			</p>


			<p class="grid-1">
				{label dState /}
				<span class="inp-fix">
					{input dState class => 'inp-text'}
				</span>
			</p>


			<p class="grid-1">
				{label dInfo /}
				<span class="inp-fix">
					{input dInfo class => 'inp-text'}
				</span>
			</p>
		</div>

		<button class="btn btn-green btn-icon-before r">
			<span><span class="icon icon-checkmark"></span> {_save_button}</span>
		</button>
	</div>
</div>

{/form}

<h2>{_'Ordered goods'}</h2>
{php $isVoucher = FALSE}
{foreach $object->items as $i}
	{if $i->type == 'product' && ($i->subType=='voucher' || $i->subType=='voucherElectronic') && $i->variantName==""}
		{php $isVoucher = TRUE}
	{/if}
{/foreach}

{if $isVoucher}
	<a n:href="generateVoucher! orderId=>$object->id" class="btn">
	<span>
			Vygenerovat slevové kódy u zakoupených poukazů
	</span>
	</a>
	<br>
	<br>
{/if}
<table>
	<thead>
		<tr>
			<th>{_product}</th>
			<th>{_amount}</th>
			<th class="right">{_price}</th>
			<th class="right">{_price_vat}</th>
		</tr>
	</thead>

	<tbody>
		{foreach $object->items as $i}
			{varType App\Model\OrderItem $i}
			<tr>
				<td>
					{if $i->type == 'product' && $i->subType=='voucher'}
						<strong>Poukázka:</strong>
					{elseif $i->type == 'product' && $i->subType=='voucherElectronic'}
						<strong>Poukázka (elektonická):</strong>
					{/if}
					<a n:href="Product:edit id=>$i->productId" n:tag-if="$i->productId">{$i->name}</a>
					{if $i->variantName}
						<span class="small">
							{if $i->type === 'product' && $i->subType === 'voucher'}
								(
									{foreach explode(', ', $i->variantName) as $code}
										{$code}
										{dump $voucherCodeByCode}
										{if isset($voucherCodeByCode[$code]) && $voucherCode = $voucherCodeByCode[$code]}
											<a n:href="voucherPdf!, voucherCodeId=>$voucherCode->id, orderItemId=>$i->id">pdf</a>
										{/if}
										{sep}, {/sep}
									{/foreach}
								)
							{else}
								({$i->variantName})
							{/if}

						</span>
					{/if}
				</td>
				<td>{$i->amount} ks</td>
				<td class="right">
					{$i->amount} &times; {$i->unitPrice|priceFormat:$object->mutation}
				</td>
				<td class="right">
					{$i->amount} &times; {$i->unitPriceDPH|priceFormat:$object->mutation}
				</td>
			</tr>
		{/foreach}
	</tbody>
	<tfoot>
		<tr>
			<td colspan="2">{_price_sum}:</td>
			<td class="right">{$object->totalPrice|priceFormat:$object->mutation}</td>
			<td class="right">{$object->totalPriceDPH|priceFormat:$object->mutation}</td>
		</tr>
	</tfoot>
</table>

<p class="right" n:if="$object->status == \App\Model\Order::STATUS_CANCEL && $object->isDeleted == 0">
	<a class="btn btn-red btn-icon-before btn-delete" n:href="deleteOrder!">
		<span><span class="icon icon-close"></span> {_delete_button}</span>
	</a>
</p>

{*
DEBUG UPLOADIFY
{form uploadForm}
	{input file_upload}
	{input save}
{/form}
*}
