<?php

namespace FrontModule;

use App\Model\BannerModel;
use App\Model\CommonTree;
use App\Model\ElasticSearch\Product\ProductGroup;
use App\Model\Tree;
use Nette\DI\Attributes\Inject;

class HomepagePresenter extends BasePresenter
{

	#[Inject]
	public BannerModel $bannerModel;


	public function __construct(
		private readonly ProductGroup $productGroup
	)
	{
	}

	public function actionDefault(): void
	{
		$this->setObject($this->mutationHolder->getMutation()->rootPage);
	}

	public function renderDefault(): void
	{
		/** @var Tree $object */
		$object = $this->getObject();
		$storePage = $this->orm->tree->getByUid('stores');

		$this->template->isHomepage = true;
		$this->template->storePage = $storePage;


		$this->template->bestsellers = $this->productGroup->findBestsellers($this->currentState, $this->priceLevel, $this->mutation);
		$this->template->news = $this->productGroup->findNews($this->currentState, $this->priceLevel, $this->mutation);


		$blogRepository = $this->orm->blog;

		// Aktuality
		/** @var CommonTree $actualitiesPage */
		$actualitiesPage = $object->getPageByUID("blog");
		$this->template->actualitiesPage = $actualitiesPage;
		$articles = $blogRepository->findByIdInPathString($actualitiesPage)
			->findBy($blogRepository->getPublicOnlyWhereParams())
			->orderBy("publicFrom", "DESC");
		$this->template->articles = $articles->limitBy(2);
		$this->template->actualities = $articles->limitBy(4);

		// Kalendář akcí
		/** @var CommonTree $calendarEventsPage */
		$calendarEventsPage = $object->getPageByUID("eventsCalendar");
		$this->template->calendarEventsPage = $calendarEventsPage;

		$today = new \DateTimeImmutable();
		$today = $today->setTime(0, 0, 0);
		$this->template->blogCalendarEvents = $calendarEventsPage->blogsPublic
				->findBy([
					'eventDateTime>=' => $today
				])
				->orderBy("eventDateTime", "ASC")->limitBy(2);
	}

}
