<?php

namespace FrontModule;

use App\FrontModule\Components\Modal\Modal;
use App\FrontModule\Components\Modal\ModalFactory;
use App\Model\BasketItemModel;
use App\Model\Link\LinkSeo;
use App\Model\Mutation;
use App\Model\ParameterValue;
use App\Model\PriceLevel;
use App\Model\Product;
use App\Model\ProductVariant;
use App\Model\Routable;
use App\Model\SeoLinkLocalization;
use App\Model\SmartMailing\SmartMailing;
use App\Model\State;
use App\Model\Tree;
use App\Model\TreeModel;
use App\PostType\Modal\Model\Orm\ModalLocalization;
use Nette;
use Nette\Application\Attributes\Persistent;
use Nette\Application\Request;
use Nette\Caching\Cache;
use Nette\DI\Attributes\Inject;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Entity\IEntity;
use SuperKoderi;
use SuperKoderi\Components\ICanonicalUrlFactory;
use SuperKoderi\Components;
use SuperKoderi\CustomField\LazyValue;
use Tracy\Debugger;

abstract class BasePresenter extends \App\BasePresenter
{
	#[Persistent]
	public string $alias;

	#[Inject]
	public Nette\Http\RequestFactory $requestFactory;

	#[Inject]
	public Components\IToggleLanguageFactory $toggleLanguageFactory;

	#[Inject]
	public Nette\Http\Session $session;

	#[Inject]
	public Nette\Caching\Storage $cacheStorage;

	#[Inject]
	public SuperKoderi\TranslatorDB $translator;

	#[Inject]
	public SuperKoderi\MutationHolder $mutationHolder;

	#[Inject]
	public SuperKoderi\MutationDetector $mutationDetector;

	#[Inject]
	public SuperKoderi\Basket $basket;

	#[Inject]
	public SuperKoderi\Parameters $parameters;

	#[Inject]
	public SuperKoderi\LinkFactory $linkFactory;

	#[Inject]
	public SuperKoderi\LastVisitCookie $lastVisitCookie;

	#[Inject]
	public SuperKoderi\IVisitedProductFactory $visitedProductFactory;

	#[Inject]
	public SuperKoderi\IProductCompareFactory $productCompareFactory;

	#[Inject]
	public LinkSeo $linkSeo;

	#[Inject]
	public SuperKoderi\ImageResizerWrapper $imageResizerWrapper;

	#[Inject]
	public Components\IContactFormFactory $contactFormFactory;

	#[Inject]
	public Components\ILangSwitcherFormFactory $langSwitcherFormFactory;

	#[Inject]
	public Components\INewsletterFormFactory $newsletterFormFactory;

	#[Inject]
	public Components\ISignInFormFactory $signInFormFactory;

	#[Inject]
	public Components\IBreadcrumbFactory $breadcrumbFactory;

	#[Inject]
	public Components\IOrderBasketFactory $orderBasketFactory;

	#[Inject]
	public Components\IMenuFactory $menuFactory;

	#[Inject]
	public Components\IUserMenuFactory $userMenuFactory;

	#[Inject]
	public Components\IRobotsFactory $robotsFactory;

	#[Inject]
	public ICanonicalUrlFactory $canonicalUrlFactory;

	#[Inject]
	public TreeModel $treeModel;

	#[Inject]
	public BasketItemModel $basketItemModel;

	#[Inject]
	public ModalFactory $modalFactory;

	protected Entity $object;

	protected Mutation $mutation;

	protected Cache $cache;

	#[Inject]
	public SmartMailing $smartMailing;

	/**
	 * state = kolize s persistant v NewsletterPresenter
	 */
	protected State $currentState;

	protected PriceLevel $priceLevel;

	protected SuperKoderi\VisitedProduct $visitedProduct;

	protected ?SeoLinkLocalization $seoLink = null;

	protected function startup(): void
	{
		// ******* redirects ************************
		if (isset($_GET['terminate'])) {
			$this->terminate();
		}

		if ($this->request->hasFlag(Request::RESTORED)) { // reseni pro backlink po ajaxu
			$this->redirect('this');
		}

		// ******* basic ************************
		parent::startup();

		$this->dbaLog->register();
		$this->setMutation();
		$this->setState();
		$this->setPriceLevel();
		$this->cache = new Cache($this->cacheStorage);

//		$this->autoCanonicalize = FALSE;

		// ******* helpers & templates ************************
		\App\Infrastructure\Latte\Filters::$mutation = $this->mutationHolder->getMutation();
		\App\Infrastructure\Latte\Filters::$translator = $this->translator;
		\App\Infrastructure\Latte\Filters::$version = $this->configService->get('webVersion');

		// prepsani defaultnich hlasek
		Nette\Forms\Validator::$messages[Nette\Forms\Form::EMAIL] = 'form_valid_email';
		Nette\Forms\Validator::$messages[Nette\Forms\Form::FILLED] = 'form_error';

		$this->basket->setMutation($this->mutation);
		$this->basket->setPriceLevel($this->priceLevel);
		$this->basket->setState($this->currentState);
		$this->basket->setTransportPayments(); // iteruje items = je potreba volat az po setnuti M+PL+S
		$this->basket->initCheck();


		// ******* other ************************
	}

	/** @noinspection PhpFieldAssignmentTypeMismatchInspection */
	protected function setMutation(): void
	{
		if ($this->getParameter('mutation')) {
			$this->mutation = $this->getParameter('mutation');
		} else {
			$this->mutation = $this->mutationDetector->detect();
		}

		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
	}


	/**
	 * Urceni statu podle cookie (user si nekde zvolil stat)
	 * nebo defaultne prvni stat mutace
	 *
	 * @throws \App\Exceptions\LogicException
	 */
	protected function setState(): void
	{
		$currentState = null;
		$idState = (int) $this->getHttpRequest()->getCookie(State::COOKIE_NAME_SELECTED_STATE);

		if ($idState) {
			$currentState = $this->orm->state->getById($idState);
		}

		if (!$currentState) {
			$currentState = $this->mutation->states->toCollection()->fetch();
		}

		if (!$currentState) {
			throw new \App\Exceptions\LogicException('Unknown current state');
		}

		$this->currentState = $currentState;
	}

	/**
	 * Defaultne zakladni cenova hladina, pokud se neurci jinak
	 */
	protected function setPriceLevel(): void
	{
		$priceLevelId = ($this->userEntity !== null) ? $this->userEntity->priceLevel->id : PriceLevel::DEFAULT_ID;
		$this->priceLevel = $this->orm->priceLevel->getById($priceLevelId);
	}

	protected function beforeRender(): void
	{
		parent::beforeRender();

		// ******* basic ************************
		$this->template->setTranslator($this->translator);
		$this->template->translator = $this->translator;
		$this->template->mutation = $this->mutationHolder->getMutation();
		$this->template->imageResizer = $this->imageResizer;
		$this->layout = FE_TEMPLATE_DIR.'/@layout.latte';
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->isHomepage = $this instanceof HomepagePresenter;
		//  robots per mutation
		if ($this->mutation->langCode) {
			$this->template->currencyCode = $this->mutation->currency; //"CZK";
		}

		$this->template->object = $this->getObject();
		$this->template->parameters = $this->parameters;
		$this->template->basket = $this->basket;
		$this->template->pages = $this->mutationHolder->getMutation()->pages;
		$this->template->state = $this->currentState;
		$this->template->priceLevel = $this->priceLevel;

		// ******* callbacks ********************
		$this->template->getImage = function ($entity, $size) {
			return $this->imageResizerWrapper->getResizedImage($entity, $size);
		};

		$this->template->cfg = function () {
			return call_user_func_array([$this->configService, 'get'], func_get_args());
		};

		// ******* other ********************
		if (isset($this->object->template) && $this->object->template) {
			$_presenterName = explode(":", $this->object->template);
			if (isset($_presenterName[0])) {
				if (isset($this->object->parent) && $this->object->parent === NULL) {
					$this->template->presenterName = "Homepage";
				} else {
					$this->template->presenterName = $_presenterName[0];
				}
			}
		}

		$this->template->googleAnalyticsCode = $this->mutationHolder->getMutation()->getGACode();
		$this->template->googleApiKey = $this->configService->getParam('google', 'apiKey');
		$this->template->linkSeo = $this->linkSeo;
//		$this->setTemplateMeasureIp();

		// ******* eshop ********************
		$this->handleBestSeller();
		$this->handleHeaderTopBar();
		$this->handleLastVisited();
		$this->handleCompareOnEveryPage();

	}


	public function getTemplateFile(string $template): string
	{
		$file = $this->template->templates . '/' . $template;

		return $file;
	}


	protected function handleBestSeller(): void
	{
		if ($this->configService->getParam('shop', 'enableBestSeller')) {
			if ( ! $this instanceof OrderPresenter) {
				// TODO REF
//				$bestSellerList = $this->productService->getBestSeller($this->object);
//				$this->template->bestSellerList = new \ArrayIterator($bestSellerList->data);
			}
		}
	}

	protected function handleLastVisited(): void
	{
		if ($this->configService->getParam('shop', 'enableLastVisited')) {
			$this->visitedProduct = $this->visitedProductFactory->create();

			if ( ! ($this instanceof OrderPresenter)) {
				$this->template->visitedProductList = $this->visitedProduct->getAllShort($this->presenter->getName(), $this->getParameters());
				$this->template->visitedProductListCount = $this->visitedProduct->getCount($this->presenter->getName(), $this->getParameters());
			}
		}
	}

	protected function handleCompareOnEveryPage(): void
	{
		if ($this->configService->getParam('shop', 'enableCompareOnEveryPage')) {
			$compare = $this->productCompareFactory->create();
			$this->template->compareProducts = $compare->getAll();

			$compareParams = [];
			foreach ($this->template->compareProducts as $varId => $i) {
				$compareParams[] = $varId;
			}
			$this->template->compareParams = implode("-", $compareParams);
		}
	}

	protected function handleMeasureIp(): void
	{
		$this->template->measureIp = base64_encode($this->configService->get('REMOTE_ADDR'));

		if ($this->userEntity && isset($this->userEntity->id)) {
			$this->template->measureUserId = $this->userEntity->id;
			if ($this->userEntity->createdTime) {
				$this->template->measureUserCreated = $this->userEntity->createdTime->format("Y-d-m");
			} else {
				$this->template->measureUserCreated = null;
			}
		} else {
			$this->template->measureUserId = null;
			$this->template->measureUserCreated = null;
		}
	}

	/**
	 * cookies horni listu
	 */
	protected function handleHeaderTopBar(): void
	{
		$this->template->cookieName = 'firstOrderDiscount';
		$this->template->showHeaderTop = FALSE;

		if ($this->user->isLoggedIn()) {
			return;
		}
		// prijde poprve - vytvorim "trvalou" cookie firstVisit s hodnotou datumu prvni nastevy
		// prijde znovu do 30 dní, detekce firstVisit cookies, podle datumu
		// prijde po 30 dnech, detekce firstVisit cookies, podle datumu

		// pokud zatrhne krizek pred 30. dnem -> skryji listu na cca 1 den
		// pokud zatrhne krizek po 30. dnu, -> kryji listu trvale

		// + prodlouzovat platnost cookie pokud je starsi jak 300 dni

//		bd("handleHeaderTopBar");
		try {


			$firstVisitTime = $this->getHttpRequest()->getCookie('firstVisit');
			if (!$firstVisitTime) {
				$now = new \DateTime();
//			$now->setDate(2017, 11, 1); // test
				$this->getHttpResponse()->setCookie('firstVisit', $now->format('Y-m-d'), new Nette\Utils\DateTime('+ 3 years'));
				$firstMonth = TRUE;

			} else {
				$fW = new \DateTime($firstVisitTime);
				$now = new \DateTime();
				$diff = $fW->diff($now);

				if ($diff->days < 31) { // min jak 30 dnu
					$firstMonth = TRUE;
				} else {
					if ($diff->days > 300) {
						// prodlouzeni platnosti cookies
						$this->getHttpResponse()->setCookie('firstVisit', $fW->format('Y-m-d'), new Nette\Utils\DateTime('+ 3 years'));
					}
					$firstMonth = FALSE;
					$this->template->cookieName = 'firstOrderDiscountP';
				}
			}

			$cookieHeaderTopUnder30 = $this->getHttpRequest()->getCookie('firstOrderDiscount');
			$cookieHeaderTopAfter30 = $this->getHttpRequest()->getCookie('firstOrderDiscountP');

			if ($firstMonth) {
				if ($cookieHeaderTopUnder30) {
					$this->template->showHeaderTop = FALSE;
				} else {
					$this->template->showHeaderTop = TRUE;
				}

			} else {
				if ($cookieHeaderTopAfter30 || $cookieHeaderTopUnder30) {
					$this->template->showHeaderTop = FALSE;
				} else {
					$this->template->showHeaderTop = TRUE;
				}
			}
		} catch (\Throwable $e) {

		}

	}


	public function setObject(Entity $object): void
	{
		$this->object = $object;
	}


	public function getObject(): Entity
	{
		return $this->object;
	}


	public function handleLogout(): void
	{
		$this->getUser()->logout(TRUE);
		$this->flashMessage('msg_info_logout');
		$this->redirect('this');
	}


	public function handleCompareRemove(int $productId): void
	{
		$productCompare = $this->productCompareFactory->create();
		$productCompare->removeAll();
	}


	public function handleCompareClick(int $productId, mixed $ids = NULL): void
	{
//		bd("ids:". $ids);
		$newIds = NULL;
		if ($ids) {
			$x = explode("-", $ids);
			$_newIds = [];
			foreach ($x as $i) {
				if ($productId != $i) {
					$_newIds[] = $i;
				}
			}
			$newIds = implode("-", $_newIds);
		}

		$productCompare = $this->productCompareFactory->create();
		$productVariant = $this->orm->productVariant->getById($productId);
		if ($productCompare->isProductInList($productId)) {
			$productCompare->remove($productVariant);
		} else {
			$productCompare->add($productVariant);
		}

		assert(property_exists($this->object, 'uid'));
		if ($this->object->uid == "compare") {
//			$newIds = $productCompare->getAllQuick();
			if ($newIds) {
//				bd("new IDs:".$newIds);
				$this->redirect('this', ['ids' => $newIds]);
			} else {
				$this->redirect($this->mutation->pages->compare);
			}

		} else {
			if ($this->presenter->isAjax()) {
				$this->presenter->redrawControl();
			} else {
				$this->redirect('this');
			}
		}
	}

	public function getBestSellerBrands(): array
	{
		// zjisteni nejprodavanejsich znacek
		// pokud nejsou nastavene rucne (TODO)
		// musíme je dopočítat
		$brands = [];
		$brandsOrder = [];
		foreach ($this->orm->product->findBestseller($this->mutation)->limitBy(20) as $item) {
			/** @var ParameterValue|null $brand */
			$brand = $item->getParameterValueByUid("manufacturer");
			if ($brand && isset($brand->internalValue)) {
				$brands[$brand->internalAlias] = $brand;
				if (isset($brandsOrder[$brand->internalAlias])) {
					$brandsOrder[$brand->internalAlias] += 1;

				} else {
					$brandsOrder[$brand->internalAlias] = 1;
				}
			}
		}
		arsort($brandsOrder);
		foreach ($brandsOrder as $k => $i) {
			$brandsOrder[$k] = $brands[$k];
		}
		return $brandsOrder;
	}


	// ************************** COMPONENTS ****************************** /

	protected function createComponentOrderBasket(): Components\OrderBasket
	{
		return $this->orderBasketFactory->create($this->mutation);
	}

	protected function createComponentBreadcrumb(): Components\Breadcrumb
	{
		return $this->breadcrumbFactory->create($this->getObject());
	}

	protected function createComponentSignInForm(): Components\SignInForm
	{
		return $this->signInFormFactory->create($this->object);
	}

	protected function createComponentSignInFormHeader(): Components\SignInForm
	{
		return $this->signInFormFactory->create($this->object);
	}

	protected function createComponentMenu(): Components\Menu
	{
		return $this->menuFactory->create($this->getObject());
	}

	protected function createComponentUserMenu(): Components\UserMenu
	{
		/** @var Routable|null|Entity $object */
		$object = $this->getObject();
		if ($object instanceof Tree) {
			\assert($object instanceof Routable);
		} else {
			$object = null;
		}


		return $this->userMenuFactory->create($object);
	}

	protected function createComponentContactForm(): Components\ContactForm
	{
		return $this->contactFormFactory->create($this->object);
	}

	protected function createComponentNewsletterForm(): Components\NewsletterForm
	{
		return $this->newsletterFormFactory->create();
	}

	protected function createComponentCanonicalUrl(): Components\CanonicalUrl
	{
		if (isset($this['pager'])) {
			$paginator = $this['pager'];
		} else {
			$paginator = null;
		}

		return $this->canonicalUrlFactory->create();

	}


	protected function createComponentLangSwitcherForm(): Components\LangSwitcherForm
	{
		return $this->langSwitcherFormFactory->create($this->getObject(), $this->mutationHolder->getMutation());
	}

	protected function createComponentLangSwitcherFormOver(): Components\LangSwitcherForm
	{
		return $this->langSwitcherFormFactory->create($this->getObject(), $this->mutationHolder->getMutation());
	}

	protected function createComponentRobots(): Components\Robots
	{
		return $this->robotsFactory->create($this->getObject(), $this->mutationHolder->getMutation(), $this->seoLink);
	}

	public function createComponentToggleLanguage(): Components\ToggleLanguage
	{
		return $this->toggleLanguageFactory->create($this->getObject(), $this->mutationHolder->getMutation());
	}


	// ****************************** INTERNALS ****************************** /


	/**
	 * @param array $args
	 * @throws Nette\Application\UI\InvalidLinkException
	 */
	public function link(LazyValue|Routable|Product|ProductVariant|string $destination, $args = []): string
	{
		[$destination, $args] = $this->translateDestination($destination, $args);

		if ($destination instanceof Routable) {
			[$destination, $args] = $this->linkFactory->linkInPresenter($destination, $args);
		}

		return parent::link($destination, $args);
	}


	/**
	 * @throws Nette\Application\AbortException
	 */
	public function redirect(LazyValue|Routable|Product|string $destination, $args = []): never
	{
		[$destination, $args] = $this->translateDestination($destination, $args);

		if ($destination instanceof Routable) {
			[$destination, $args] = $this->linkFactory->linkInPresenter($destination, $args);
		}

		parent::redirect($destination, $args);
	}


	private function translateDestination(LazyValue|Routable|Product|ProductVariant|string $destination, array $args): array
	{
		if ($destination instanceof LazyValue) {
			$destination = $destination->getEntity();
			if ($destination === null) {
				trigger_error('Bad CF LazyValue entity', E_USER_NOTICE);
				// value for common user without debug mode
				$destination = 'this';
			}
		}

		if (is_string($destination)) { // input: this, //this, logout!
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
		}

		if ($destination instanceof Product) {
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
			$destination = $destination->getLocalization($mutation);
		}

		if ($destination instanceof ProductVariant) {
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
			$destination = $destination->product->getLocalization($mutation);
		}

		return [$destination, $args];
	}

	public function handleTestSmartMail(): void //remove
	{
		$aF = [
			"street"=>"ulice",
			"city"=>"city",
			"zip"=>"77777",
			"phone"=>"*********",
			"company"=>"raaa",
		];

		$cF = [
			$this->configService->getParam('smartmailing', 'paramRegistrated') => "ANO",
		];

		//$this->smartMailing->sendContact("<EMAIL>", "Petr", "Šebela", 4, "confirmed", $cF, $aF);
		//$this->smartMailing->updateContact("<EMAIL>", "Aha", "SSS", $cF, $aF);
		//$this->smartMailing->removeContact("<EMAIL>", 1);
		//$this->smartMailing->unsubscribeContact("<EMAIL>", 4);
		//$this->smartMailing->isSubscriber("<EMAIL>", 4);
		//$this->smartMailing->getContactIdByEmail("<EMAIL>");
		die;
	}

	protected function createComponentSignInFormStep2(): Components\SignInForm
	{
		return $this->signInFormFactory->create($this->object);
	}

	public function createComponentModal(): Modal
	{
		return $this->modalFactory->create($this->mutation);
	}

}
