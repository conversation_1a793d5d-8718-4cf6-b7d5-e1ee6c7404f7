{if count($breadcrumbs) > 0}

	<script type="application/ld+json">
	{
		"@context": "https://schema.org",
		"@type": "BreadcrumbList",
		"itemListElement": [
		{foreach $breadcrumbs as $key=>$i}
			{
				"@type": "ListItem",
				"position": {$key+1},
				"name": {$i->nameAnchor},
				"item": {$mutation->getBaseUrlWithPrefix()."/".$i->alias}
			}{if !$iterator->last},{/if}
		{/foreach}
		]
	}
	</script>
{/if}
