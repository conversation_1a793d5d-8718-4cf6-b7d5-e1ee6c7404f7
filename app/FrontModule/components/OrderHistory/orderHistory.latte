{varType App\Model\Order[] $orders}

{* <h1>{$object->name}</h1> *}

{snippet orderHistory}
{default $showAll = false}

{* ********************* filtr ************************* *}
{* {form filterForm class => "form-filter"}
	<p>
		{_'filter_order_status'}:
		{foreach $form['status']->items as $key => $label}
			<label>
				<input n:name="status:$key">
				<span>
				{$label}
			</span>
			</label>
		{/foreach}
	</p>

	{include '../inp.latte', form=>$form, name=>from}
	{include '../inp.latte', form=>$form, name=>to}
	{include '../inp.latte', form=>$form, name=>number}

	<p>
		<button n:name="filter" class="btn">
				<span class="btn__text">
					<span>
						{_'btn_filter'}
					</span>
				</span>
		</button>
		<a n:href="clearFilter!" class="btn" n:if="$filterSession->data">
			{_'btn_filter_cancel'}
		</a>
	</p>
{/form} *}


{* ********************* vypis objednavek ************************* *}

{if $orders->count()}
	<div class="c-orders u-mb-lg">
		<h2 class="c-orders__title">{_'order_title'}</h2>
		<div class="c-orders__list">
			<div class="c-orders__item u-table-responsive">
				<table class="c-orders__table">
					<thead>
						<tr>
							<th>
								{_'order_number'}
							</th>
							<th>
								{_'order_status'}
							</th>
							{* <th>
								{_'order_amount'}
							</th> *}
							<th>
								{_'order_created'}
							</th>
							<th class="c-orders__cell c-orders__cell--secondary">
								{_'order_delivery'}
							</th>
							<th class="c-orders__cell c-orders__cell--secondary">
								{_'order_payment'}
							</th>
							<th>
								{_'order_total_price_vat'|stripHtml}
							</th>
							<th class="c-orders__cell c-orders__cell--secondary">&nbsp;</th>
						</tr>
					</thead>
					<tbody>
					{foreach $orders as $order}
						<tr>
							<td>
								<a href="{plink this $order->hash}" title="{_'order_link_detail'}">
									{$order->number}
								</a>
							</td>
							<td>

								{_'order_status_' . $order->status}
							</td>
							{* <td>
								{$order->products->count()}
							</td> *}
							<td>
								{$order->created|date:'j. n. Y'}
							</td>
							<td class="c-orders__cell c-orders__cell--secondary">
								{$order->transport->name}
							</td>
							<td class="c-orders__cell c-orders__cell--secondary">
								{$order->payment->name}
							</td>
							<td>{$order->totalPriceDPH|priceFormat:$order->mutation}</td>
							<td class="c-orders__cell c-orders__cell--secondary">
								<a href="{plink this $order->hash}">
									{_'order_link_detail'}
								</a>
							</td>
						</tr>
					{/foreach}
					</tbody>
				</table>

				{* {if $order->products->count()}
					<table>
						<thead>
							<tr>
								<td>
									{_title_product_buy}
								</td>
								<td>
									{_amount}
								</td>
								<td>
									{_unit_price_vat}
								</td>
								<td>
									{_total_price_vat}
								</td>
							</tr>
						</thead>
						<tbody>
							{foreach $order->products as $productOrderItem}
								{*dump $productOrderItem->product->alias}
								{dump $productOrderItem->variant*}

								{* <tr>
									<td>
										{var $isSpan = $productOrderItem->product && $productOrderItem->product->alias ? null : 'span'}
										{var $link = $isSpan ? null : ($productOrderItem->variant ? $presenter->link($productOrderItem->product, ['v'=>$productOrderItem->variant->id]) : $presenter->link($productOrderItem->product))}

										<a n:tag="$isSpan"{if $link} href="{$link}"{/if}>
											{$productOrderItem->name}
										</a>
									</td>
									<td>
										{$productOrderItem->amount} {_count_short}
									</td>
									<td>
										{$productOrderItem->unitPriceDPH|priceFormat} / {_count_short}
									</td>
									<td>
										{$productOrderItem->totalPriceDPH|priceFormat}
									</td>
								</tr>

								{if $productOrderItem->services}
									{foreach $productOrderItem->services as $i}
										<tr>
											<td>
												{$i->name}
											</td>
											<td>
												{$productOrderItem->amount} {_count_short}
											</td>
											<td>
												{$i->unitPriceDPH|priceFormat} / {_service}
											</td>
											<td>
												{$productOrderItem->amount*$i->unitPriceDPH|priceFormat}
											</td>
										</tr>
									{/foreach}
								{/if}
							{/foreach}
						</tbody>
					</table>
				{/if} *}

				{* {if $order->vouchers->count() || $order->discounts->count()}
					<table>
						<thead>
							<tr>
								<td>
									{_title_voucher}
								</td>
								<td>
									{_total_price_vat}
								</td>
							</tr>
						</thead>
						<tbody>
							{if $order->vouchers->count()}
								{foreach $order->vouchers as $voucherOrderItem}
									<tr>
										<td>
											{$voucherOrderItem->name}
										</td>
										<td>
											{$voucherOrderItem->unitPriceDPH|priceFormat}
										</td>
									</tr>
								{/foreach}
							{/if}

							{if $order->discounts->count()}
								{foreach $order->discounts as $voucherOrderItem}
									<tr>
										<td>
											{$voucherOrderItem->name}
										</td>
										<td>
											{$voucherOrderItem->unitPriceDPH|priceFormat}
										</td>
									</tr>
								{/foreach}
							{/if}
						</tbody>
					</table>
				{/if} *}

				{* {foreach $order->products as $productOrderItem}
					{if $productOrderItem->presents}
						{foreach $productOrderItem->presents as $present}
							<p>
								{if isset($present->product->alias)}
									<a n:href="$present->product">
										{$present->name}
									</a>
								{else}
									{$present->name}
								{/if}
							</p>
						{/foreach}
					{/if}
				{/foreach} *}
			</div>
		</div>

		{if $showAll}
			<p class="c-orders__btn">
				<a href="#" class="btn btn--secondary">
					<span class="btn__text">
						<span>
							{_'btn_show_all_orders'}
						</span>
					</span>
				</a>
			</p>
		{/if}
	</div>

	{control pager}


{else}
	<div class="message">
		{_'message_no_order_history'}
	</div>
{/if}

{/snippet}

