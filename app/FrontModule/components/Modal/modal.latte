<!-- TODO-MEZSUP-23 - implement FE -->
{block content}

	{if $modalLocalization}
		<div id="modal-{$modalLocalization->id}" class="b-modal b-modal--custom is-opened">
			<div class="b-modal__inner">
				<a n:href="closeModal!, $modalLocalization->id" class="b-modal__close js-modal-close">
					{('close')|icon}
					<span class="u-vhide">Close</span>
				</a>
				<div class="b-modal__content">
					<div class="b-modal__title">
						<h2>{$modalLocalization->cf->base->title}</h2>
					</div>
					<div class="b-modal__text">
						{$modalLocalization->cf->base->content|noescape}
					</div>
				</div>
			</div>
		</div>
	{/if}

{/block}
