<?php

namespace SuperKoderi\Components;

use App\Model\Mutation;
use App\Model\Orm;
use App\Model\UserHash;
use App\Model\UserHashModel;
use App\Model\UserModel;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Entity\Entity;
use SuperKoderi\Email\ICommonFactory;
use SuperKoderi\hasMessageForFormComponentTrait;
use SuperKoderi\TranslatorDB;

/**
 * @property-read DefaultTemplate $template
 */
class LostPasswordForm extends UI\Control
{
	use hasMessageForFormComponentTrait;

	private Mutation $mutation;

	public function __construct(
		private Entity $object,
		private ?string $hash,
		private TranslatorDB $translator,
		private Orm $orm,
		private ICommonFactory $commonEmailFactory,
		private UserModel $userModel,
		private UserHashModel $userHashModel,
	)
	{
		$this->mutation = $this->orm->getMutation();
	}


	public function render(): void
	{
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/lostPasswordForm.latte');
	}


	public function renderReset(): void
	{
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/resetPasswordForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);
		$form->addEmail('username', 'email')
			->setRequired('form_enter_username');

//		if ($this->hash) {
//			$form->addText('username', 'email')
//				->setRequired('form_enter_username');
//		} else {
//
//		}

		if (isset($_GET['email'])) {
			$form->setDefaults(['username' => $_GET['email']]);
		} elseif ($this->presenter->getHttpRequest()->getCookie(SignInForm::COOKIE_NAME_LOGIN_EMAIL_AUTH_ERROR)) {
			$form->setDefaults(['username' => $this->presenter->getHttpRequest()->getCookie(SignInForm::COOKIE_NAME_LOGIN_EMAIL_AUTH_ERROR)]);
		}

		$form->addSubmit('send', 'Send');

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'formSuccess'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formSuccess(UI\Form $form): void
	{
		$valuesAll = $form->getHttpData();

		$userEntity = $this->orm->user->getByEmail($valuesAll['username'], $this->mutation);

		if ($userEntity && $userEntity->id) {
			$valuesAll['email'] = $userEntity->email;
			$userHash = $this->userHashModel->generateHashForUser($userEntity, UserHash::TYPE_LOST_PASSWORD, [$valuesAll['email']], 1);
			$valuesAll['link'] = $this->mutation->getBaseUrlWithPrefix().$this->presenter->link($this->orm->getMutation()->pages->resetPassword, ['hashToken' => $userHash->hash]);

			$this->commonEmailFactory
				->create()
				->send('', $valuesAll['email'], 'lostPassword' , (array) $valuesAll);


			$this->flashMessage('form_send_reset_password', 'ok');

		} else {
			$form['username']->addError("user_not_found");
			$this->flashMessage('user_not_found', 'error');
		}

		$form['username']->setValue('');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->redirect('this');
		}
	}


	protected function createComponentFormReset(): UI\Form
	{
		$form = new UI\Form;
		bd($this->hash);
		$form->setTranslator($this->translator);
		$form->addHidden('hash', $this->hash);
		$form->addPassword('password', 'form_label_password')->setRequired();
		$form->addPassword('passwordVerify', 'form_label_password2')
			->setRequired()
			->addRule(UI\Form::EQUAL, 'form_password_not_same', $form['password']);

		$form->addSubmit('send', 'Send');

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'formResetSuccess'];
		$form->onError[] = [$this, 'formResetError'];
		return $form;
	}


	public function formResetError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formResetSuccess(UI\Form $form): void
	{
		$valuesAll = $form->getHttpData();

		bd($valuesAll);
		$hashObject = $this->orm->userHash->getBy(['hash' => $valuesAll['hash']]);

		$userEntity = $hashObject->user;

		if ($this->userModel->save($userEntity, $valuesAll, $userEntity->id) !== false) {
			$this->userModel->deleteHash($userEntity, $hashObject);
			$this->flashMessage('form_reset_password', 'ok');
			$this->presenter->flashMessage('form_reset_password', 'ok');


		}

		if (!$userEntity->id) {

			$form['password']->addError("user_not_found");
			$this->flashMessage('user_not_found', 'error');
		}

		$form['password']->setValue('');
		$form['passwordVerify']->setValue('');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect($this->mutation->pages->userLogin);
		}
	}

}


interface ILostPasswordFormFactory
{
	public function create(Entity $object, ?string $hash): LostPasswordForm;
}

