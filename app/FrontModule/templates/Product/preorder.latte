{block content}

	{if $presenter->isAjax()}
		{*THICKBOX*}
		{control preorder}
	{else}
		<div class="s-std s-std--thin m-hide">
			<div class="row-main">
				{control breadcrumb}
				{include '../part/box/tools.latte'}
			</div>
		</div>
		<div class="s-std s-std--grey">
			<div class="row-main">
				<div class="grid">
					<div class="grid__cell size--10-12">
						{include '../part/box/annot.latte'}
					</div>
				</div>
				{include '../part/crossroad/categories.latte', crossroad=>$object->crossroad}
			</div>
		</div>

		<div class="s-std">
			<div class="row-main">
				<div class="grid">
					<div class="grid__cell size--3-12">

					</div>
					<div class="grid__cell size--9-12">
						{control preorder}
					</div>
				</div>

				{include '../part/box/products.latte', title=>products_title_last_visited, crossroad=>$visitedProductList->data}
			</div>
		</div>
	{/if}


{/block}
