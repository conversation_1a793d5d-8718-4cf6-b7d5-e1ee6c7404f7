{varType App\Model\Tree $page}
{if !isset($crossroad)}
	{default $crossroad = isset($object->crossroad) ? $object->crossroad : new ArrayIterator()}
{/if}
{default $title = h2}
{default $page = null}
{default $heading = ''}
{default $linkTitle = 'btn_show_more'}
{default $showLink = true}
{default $pager = true}
{default $class = ''}

<div n:if="$crossroad->count() > 0" n:class="c-articles, $class">
	<div class="c-articles__title c-heading-h2" n:if="$page || $heading">
		<h2>{$page ? $page->nameTitle : $heading|translate}</h2>
		<div class="c-heading-h2__more" n:ifcontent n:if="$showLink">
			<a n:ifset="$page" n:href="$page" class="c-heading-h2__link">
				{$linkTitle|translate}
			</a>
		</div>
		{var $title = h3}

	</div>
	<div class="c-articles__list grid grid--eq">
		{foreach $crossroad as $c}
			<div class="c-articles__item grid__cell size--6-12@md {if $class != 'c-articles--hp'}size--3-12@lg{/if}">
				{include '../box/article.latte', c: $c, class: '', title: $title}
			</div>
		{/foreach}
	</div>

	{if $pager}
		{control pager, [class => '']}
	{/if}

</div>
