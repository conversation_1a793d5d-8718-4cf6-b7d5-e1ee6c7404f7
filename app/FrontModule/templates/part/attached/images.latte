{default $object = $object}
{default $class = 'u-mb-lg'}
{default $minimal = 0}

{if $object->images && $object->images->count() > $minimal &&
( ( $object->hideFirstImage && $object->images && $object->images->count() > ($minimal + 1) ) || !$object->hideFirstImage )}
	<section class="b-images {$class}">
		<div class="row-main row-main--small">
			<h2 class="b-images__title c-heading-h2">
				{_'title_images'}
			</h2>
			<div class="b-images__list grid">
				{if $object->images && $object->images->count() && (($object->hideFirstImage && $object->images && $object->images->count() > 1) || !$object->hideFirstImage )}
					{foreach $object->images as $k=>$i}
						{if (!$object->hideFirstImage && $iterator->counter > $minimal) || ($object->hideFirstImage && $iterator->counter > ($minimal + 1))}
							<div class="b-images__item grid__cell size--6-12 size--4-12@md">
								{if $i->isSvg}
									{php $img = $i->getSize('')}
									<a href="{$img->src}" class="b-images__link">
										{file_get_contents($img->srcRelative)|noescape}
									</a>
								{else}
									{php $img = $i->getSize('sm')}
									{php $imgLg = $i->getSize('xl')}

									<a href="{$imgLg->src}" class="b-images__link">
										<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy" >
									</a>
								{/if}
							</div>
						{/if}
					{/foreach}
				{/if}
			</div>
		</div>
	</section>
{/if}
