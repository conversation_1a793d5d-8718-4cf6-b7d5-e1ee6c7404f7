{include sectionSystemMessages}

<header class="header" data-controller="HeaderToggle">
	<div class="row-main">
		<div class="header__container">
			<{if $isHomepage}h1{else}p{/if} class="header__logo b-logo">
				<a n:href="$pages->title" class="b-logo__link">
					<img src="/static/img/logo.svg" alt="{_logo}" width="135" height="36">
					<span class="u-vhide">{_'logo_heading'}</span>
				</a>
			</{if $isHomepage}h1{else}p{/if}>
			<nav class="m-header-toggle header__item header__item--right u-d-n@lg">
				<div class="m-header-toggle__nav">
					<ul class="m-header-toggle__list">
						{* search *}
						<li class="m-header-toggle__item">
							<a n:href="$pages->search" aria-label="{_'btn_search'}" aria-expanded="false" aria-controls="id-search" class="m-header-toggle__link b-toggle" data-controls="id-search" data-headerToggle-target="toggle" data-action="HeaderToggle#toggle">
								{('search')|icon: 'b-toggle__icon'}
								<span class="u-vhide">{_'btn_search'}</span>
								<span class="b-toggle__close"></span>
							</a>
						</li>
						{* user *}
						<li class="m-header-toggle__item">
							{if $user->loggedIn}
								<a n:href="$pages->userSection" aria-expanded="false" aria-controls="id-user" class="m-header-toggle__link" data-controls="id-user" data-headerToggle-target="toggle" data-action="HeaderToggle#toggle">
								<span class="m-header-toggle__item-badge u-font-symbol">&#10003;</span>
							{else}
								<a n:href="$pages->userLogin" class="m-header-toggle__link" data-naja data-naja-loader="body" data-naja-modal="snippet--signInFormAjax" data-custom-class="b-modal--small" data-naja-history="off">
							{/if}
								{('user')|icon}
								<span class="u-vhide">
									{if $user->loggedIn}
										{_'profile_title'}
									{else}
										{_'login_title'}
									{/if}
								</span>
							</a>
						</li>
						{* basket *}
						<li class="m-header-toggle__item">
							<a n:href="$pages->basket" aria-label="{_'btn_basket'}">
								{('cart-shopping')|icon}
								<span class="u-vhide">{_'btn_basket'}</span>
								{* <span n:if="$basket->getProductsCount()" class="m-header-toggle__item-badge">{$basket->getProductsCount()}</span> *}
							</a>
						</li>
						<li class="m-header-toggle__item">
							<button type="button" aria-label="{_'btn_menu'}" aria-expanded="false" class="m-header-toggle__link b-menu-toggle" data-controls="id-menu" data-headerToggle-target="toggle" data-action="HeaderToggle#toggle">
								<span class="b-menu-toggle__icon"></span>
								<span class="u-vhide">{_'btn_menu'}</span>
							</button>
						</li>
					</ul>
				</div>
			</nav>
			<div id="id-menu" class="header__item header__item--menu" data-headerToggle-target="collapse" data-collapse="id-menu">
				<nav class="m-main m-main--component" id="menu-main" data-controller="Menu">
					{* {control menu} *}
					{include $presenter->getTemplateFile('part/menu/main.latte'), menu: $mutation->cf->eshopMenu, class: '', type: 'eshop'}
					{include $presenter->getTemplateFile('part/menu/social-networks.latte'), menu: $mutation->cf->menu_social_networks, direction: 'horizontal', class: 'u-mb-0'}
				</nav>
				<div class="header__item header__item--content-menu">
					{include $presenter->getTemplateFile('part/menu/main.latte'), menu: $mutation->cf->menu_main, class: 'm-main--content'}
					<div class="header__item header__item--btn u-d-n u-d-b@lg">
						{include 'box/login.latte'}
					</div>
					<div n:snippet="basketHeader" class=" header__item header__item--btn u-d-n u-d-b@lg">
						{control orderBasket:small}
					</div>
				</div>
			</div>

			<div id="id-user" data-headerToggle-target="collapse"  data-collapse="id-user" class="u-d-n@md u-no-print">
				{include 'box/login.latte'}
			</div>
			{* <div data-headerToggle-target="collapse" data-collapse="id-basket" class="u-d-n@md">
				{control orderBasket:small}
			</div> *}

			<div id="id-search" class="header__item header__item--search" data-headerToggle-target="collapse" data-collapse="id-search">
				{include 'form/search.latte'}
			</div>
		</div>
	</div>
</header>

{define sectionSystemMessages}
	{if isset($mutation->cf->system_messages->text)}
		{php $messageHash = hash('ripemd160', $mutation->cf->system_messages->text)}

		{if !(isset($_COOKIE['systemMessages'])) || (isset($_COOKIE['systemMessages']) && ($_COOKIE['systemMessages'] != $messageHash))}
			<div class="b-system-messages" n:ifcontent data-controller="SystemMessages" data-hash="{$messageHash}">
				{var $item = $mutation->cf->system_messages}
				<div class="message" n:if="isset($item->text)">
					<div class="row-main">
						<div class="message__content">
							{if isset($item->icon)}{($item->icon)|icon: 'b-system-messages__icon'}{/if}
							<div class="u-text-content" n:ifcontent>
								<p n:ifcontent>
									{$item->text}
									<a n:if="isset($item->link->text) && isset($item->link->url)" href="{$item->link->url|noescape}">{$item->link->text|noescape}</a>
								</p>
							</div>
							<button type="button" data-action="SystemMessages#close" class="message__btn-close btn--close" title="{_'btn-close'}">
								<span class="b-modal__close-text u-vhide">{_'btn-close'}</span>
								{('close')|icon}
							</button>
						</div>
					</div>
				</div>
			</div>
		{/if}
	{/if}
{/define}
