{default $class = isset($class) ? $class : ""}
{default $type = isset($type) ? $type : ""}

{if $type == 'eshop'}
    <ul class="m-main__list" data-menu-target="menu" n:ifset="$menu">
        {foreach ($menu ?? []) as $key => $item}
            {var $hasSubmenu = isset($item->group->subPages) ? true : false}

            {block|strip}
                {var $menuSeolink = isset($item->group->linkToggleRadio->url) ? $item->group->linkToggleRadio->url : ( isset($item->group->linkToggleRadio->seolink) ? $item->group->linkToggleRadio->seolink : false)}
                <li n:if="isset($item->group->page)"
					n:class="m-main__item, $hasSubmenu ? has-submenu,
							(isset($seoLink) && $menuSeolink) && ($menuSeolink->id == $seoLink->id) ? 'is-active'"

						data-controller="Hover TouchOpen"
						data-hover-delay-value="500"
						data-menu-target="item">
                    <a n:href="$menuSeolink"
                        data-action="TouchOpen#open"
                        n:if="$menuSeolink"
                        class="m-main__link">
                        {$item->group->page}
                        {if $hasSubmenu} {('chevron-down-regular')|icon: 'm-main__link-icon'} {/if}
                    </a>
                    <span class="m-main__link no-link" n:if="!$menuSeolink"{if $hasSubmenu} data-action="click->Menu#toggleSubmenu"{/if}>
                        {$item->group->page}
                        {if $hasSubmenu} {('chevron-down-regular')|icon: 'm-main__link-icon'} {/if}
                    </span>
                    <button type="button" class="m-main__btn" data-action="Menu#toggleSubmenu" n:if="$hasSubmenu">
                        {('chevron-right-regular')|icon}
                    </button>
                    {if $hasSubmenu}
                        {include m-submenu, menu: $item->group->subPages, level: 0}
                    {/if}
                </li>
            {/block}
        {/foreach}
    </ul>
{else}
    <nav n:class="m-main, $class" n:ifset="$menu" n:ifcontent>
        <ul class="m-main__list" n:inner-foreach="($menu ?? []) as $key => $item" n:ifcontent>

			{if in_array($item->template, ['Blog:default', 'Blog:main']) && $object instanceOf App\Model\Blog}
				{var $isActive = in_array($item->id, $object->path)}
			{else}
				{var $isActive = ($item->id == $object->id || in_array($item->id, $object->path))}
			{/if}


            <li n:class="m-main__item, $isActive ? 'is-active'">
                <a n:href="$item" class="m-main__link">
                    {$item->nameAnchor}
                </a>
            </li>
        </ul>
    </nav>
{/if}

{define m-submenu}
	{default $level = 0}
	<div n:class="m-submenu, $level ? 'm-submenu--inner' : 'm-submenu--main'" data-scroll-lock-scrollable >
		<ul class="m-submenu__list">
			{foreach ($menu ?? []) as $menuLink}
                    {var $item = isset($menuLink->link->seolink) ? $menuLink->link->seolink : $menuLink->link->url}
					<li n:class="m-submenu__item, (isset($seoLink)) && ($item->id == $seoLink->id) ? 'is-active'">
						<a href="{plink $item}" class="m-submenu__link">
							{$item->nameAnchor}
						</a>
					</li>
			{/foreach}
		</ul>
	</div>
{/define}
