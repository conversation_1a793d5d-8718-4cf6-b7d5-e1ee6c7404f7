{* {import $templates.'/part/box/products.latte'} *}
{default $class = ''}
{default $productTitle = null}
{var $variant = null}
{default $loading = "lazy"}

{if $product}
	{if $product->isVariant}
		{php $link = $presenter->link($product, ['v'=>$product->id])}
		{var $variant = $product}
		{var $product = $product->product}
	{else}
		{* je to produkt - obalka*}
		{php $link = $presenter->link($product)}
		{var $activeVariant = $product->activeVariants->fetchAll()}
		{if count($activeVariant) == 1}
			{var $variant = $activeVariant[0]}
		{/if}
	{/if}
	<a href="{$link}" class="b-suggest__link">
	<article class="b-product-suggest {$class}">

		<div class="b-product-suggest__img">
			{if $product->firstImage}
				{* {php $img = $product->firstImage->getSize('md')}
				<img src="{$img->src}" alt="" loading="lazy" width="{$img->width}" height="{$img->height}"> *}
				{include $presenter->getTemplatefile('part/core/image.latte'),
					img: $product->firstImage,
					alt: $product->nameAnchor,
					srcset: ['50w' => '1x', '100w' => '2x'],
					sizes: '50px',
					loading: $loading
				}
			{else}
				<img src="/static/img/noimg.svg" alt="" loading="{$loading}" width="50" height="50">
			{/if}
		</div>
		<div class="b-product-suggest__body">
			<h3 n:tag="$productTitle" class="b-product-suggest__title">
				{$product->nameAnchor}{* vzdy nazev produktu (obalky) *}
			</h3>
			{include productAvailability}
		</div>
		<div class="b-product-suggest__price">
			{include productPrice}
		</div>
	</article>
	</a>
{/if}

{define productPrice}
	<div class="b-product__price">
		{if $variant}
			{varType App\Model\ProductVariant $variant}
			{var $priceWithVat = $variant->priceWithVat($mutation, $priceLevel, $state)}
			{var $originalPriceWithVat = $variant->originalPriceWithVat($mutation, $priceLevel, $state)}
			{var $discountPriceWithVat = $variant->discountPriceWithVat($mutation, $state)}
			{var $isPriceForm = false}
		{else}
			{* P s vice PV *}
			{varType App\Model\Product $product}
			{var $priceWithVat = $product->priceWithVat($mutation, $priceLevel, $state)}
			{var $originalPriceWithVat = $product->originalPriceWithVat($mutation, $priceLevel, $state)}
			{var $discountPriceWithVat = $product->discountPriceWithVat($mutation, $state)}
			{var $isPriceForm = $product->hasPriceFrom($mutation, $priceLevel, $state)}
		{/if}

		<s class="b-product__price-before" n:if="$priceWithVat != $originalPriceWithVat">
				{$originalPriceWithVat|priceFormat}
		</s>

		{if $priceWithVat !== false}
			{if $priceWithVat == 0}
				{_price_not_determined}
			{else}
				<span class="b-product__price-main">
					<small n:if="$isPriceForm">
					{_'price_from'}
					</small>
					<strong>
						{$priceWithVat|priceFormat}
					</strong>
					{* {_'price_tax'}<br>
					<small>
						{$price|priceFormat} {_price_without_tax}
					</small> *}
				</span>
			{/if}
		{/if}
	</div>
{/define}

{define productAvailability}
	{if $product->isInStock}
		{if $product->totalSupplyCount < 100}
			{var $text = 'availability_in_stock_over_100', $class = 'is-warning'}
		{else}
			{var $text = 'availability_in_stock', $class = 'is-success'}
		{/if}

		{if !$variant && $product->supplyInfo->someNotAvailable}
			{var $note = 'availability_only_some_variants'}
		{/if}
	{else}
		{var $text = 'availability_soldout', $class = 'is-danger'}
	{/if}

	<div n:class="b-product__availability, $class">
		<strong>
			{_$text|stripHtml}
		</strong>
		<small n:if="isset($note)">({_$note})</small>
	</div>
{/define}
