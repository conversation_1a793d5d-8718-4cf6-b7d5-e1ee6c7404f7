{*if $activeGA *}
{*{if isset($gtmKey) && isset($googleAnalyticsCode) && $googleAnalyticsCode}*}
{*<script type="text/javascript">*}
	{*{if isset($gtmKey)}*}
		{*var pagetype = {$gtmKey};*}
	{*{/if}*}

	{*{if isset($basketId)}*}
		{*var basketId = {$basketId};*}
	{*{/if}*}

	{*{if isset($dataLayer['visitorLoginState'])}*}
		{*var visitorLoginState = {$dataLayer['visitorLoginState']};*}
	{*{/if}*}
	{*{if isset($dataLayer['visitorId'])}*}
		{*var visitorId = {$dataLayer['visitorId']};*}
	{*{/if}*}
	{*var pageCategory = [];*}
	{*{if isset($gtmProducts) && $gtmProducts->count()}*}
		{*var totalValue = 0;*}
		{*var ids = [];*}
		{*var basketItems = [];*}
		{*{foreach $gtmProducts as $gtmProduct}*}
			{*{if $gtmProduct}*}
			{*var item = [];*}
			{*item["id"] = {$gtmProduct->number};*}
			{*item["name"] = {$gtmProduct->name};*}
			{*item["price"] = {$gtmProduct->priceMy};*}
			{*item["brand"] = {if $gtmProduct->family}{$gtmProduct->family->name}{else}null{/if};*}
			{*item["category"] = {$gtmProduct->getParamValueByUID('TypMontaze')};*}
			{*item["colour"] = {$gtmProduct->getParamValueByUID('Barva')};*}
			{*item["chromaticTemperature"] = {$gtmProduct->getParamValueByUID('BarvaSvetla')};*}
			{*basketItems.push(item);*}
			{*{/if}*}
		{*{/foreach}*}
	{*{/if}*}

	{*{if isset($gtmPrice)}*}
		{*totalValue = {$gtmPrice};*}
	{*{/if}*}

	{*{if isset($dataLayerCat) && $dataLayerCat}*}
		{*{foreach $dataLayerCat as $c}*}
			{*pageCategory.push({$c->name});*}
		{*{/foreach}*}
	{*{/if}*}

	{*{if $gtmKey == "order" || $gtmKey == "cart"}*}
	{*dataLayer.push({*}
		{*'cart' : null*}
	{*});*}
	{*dataLayer.push({*}
		{*'cart' : {*}
			{*'id': basketId, // ID košíku*}
			{*'products' : basketItems*}
		{*}*}
	{*});*}
	{*dataLayer.push({*}
		{*'order' : {*}
			{*'id' : basketId,*}
			{*'revenue' : totalValue,*}
			{*'totalValue' : totalValue,*}
			{*'tax' : null,*}
			{*'delivery' : null,*}
			{*'deliveryType' : null,*}
			{*'paymentType' : null,*}
			{*'coupon' : null,*}
			{*'processingType' : null*}
		{*}*}
	{*});*}

	{*{elseif $gtmKey == "userSection"}*}
		{*{if isset($userEntity) && isset($userEntity->id)}*}
		{*dataLayer.push({*}
			{*'page' : {*}
				{*'type' : 'account',*}
				{*'kind': 'account'*}
			{*}*}
		{*});*}
		{*{elseif $object->uid=="lostPassword" || $object->uid=="resetPassword"}*}
		{*dataLayer.push({*}
			{*'page' : {*}
				{*'type' : 'account.passwordReset',*}
				{*'kind': 'account'*}
			{*}*}
		{*});*}
		{*{/if}*}
	{*{elseif $gtmKey == "purchase"}*}
	{*var currency = {$currency};*}
	{*dataLayer.push({*}
		{*'page' : {*}
			{*'type': 'purchase',*}
			{*'kind': 'purchase',*}
			{*'currencyCode': currency*}
		{*}*}
	{*});*}

	{*{elseif $gtmKey == "search"}*}

	{*var cnt = {$resultCount};*}
	{*var searchTerm = {$query};*}
	{*dataLayer.push({*}
		{*'page' : {*}
			{*'type': 'searchresults',*}
			{*'kind': 'searchresults',*}
			{*'searchresults': {*}
				{*'type': 'fulltext',*}
				{*'term': searchTerm,*}
				{*'resultCount': cnt*}
			{*}*}
		{*}*}
	{*});*}

	{*{elseif $gtmKey == "404"}*}
	{*dataLayer.push({*}
		{*'page' : {*}
			{*'type': 'error',*}
			{*'kind': 'error',*}
			{*'error': {*}
				{*'type': '404',*}
				{*'message': 'Page not found'*}
			{*}*}
		{*}*}
	{*});*}

	{*{elseif $gtmKey == "products"}*}

	{*dataLayer.push({*}
		{*'page' : {*}
			{*'type' : 'products',*}
			{*'kind': 'product',*}
			{*'products' : {*}
				{*'filters' : null*}
			{*}*}
		{*}*}
	{*});*}

	{*{elseif $gtmKey == "family"}*}
		{*var nameObject = {$object->name};*}
		{*dataLayer.push({*}
			{*'page' : {*}
				{*'type' : 'family',*}
				{*'kind': 'product',*}
				{*'family' : {*}
					{*'family' : nameObject*}
				{*}*}
			{*}*}
		{*});*}

	{*{elseif $gtmKey == "photoGroup"}*}

	{*var nameObject = {$object->family->name};*}
	{*var pager = {$pager};*}

	{*dataLayer.push({*}
		{*'page' : {*}
			{*'type' : 'product',*}
			{*'kind': 'product',*}
			{*'product' : {*}
				{*'name' : nameObject,*}
				{*'number' : pager,*}
				{*'filters' : null*}
			{*}*}
		{*}*}
	{*});*}

	{*{elseif $gtmKey == "productAdd"}*}

	{*dataLayer.push({*}
		{*'event': 'action.add.products',*}
		{*'action': {*}
			{*'add' : { // add|remove podle zvolené akce*}
				{*'products' : basketItems*}
			{*}*}
		{*}*}
	{*});*}

	{*{elseif $gtmKey == "productRemove"}*}

	{*dataLayer.push({*}
		{*'event': 'action.remove.products',*}
		{*'action': {*}
			{*'remove' : { // add|remove podle zvolené akce*}
				{*'products' : basketItems*}
			{*}*}
		{*}*}
	{*});*}




	{*{elseif $gtmKey == "product"}*}

	{*var family = {$object->family->name};*}
	{*var number = {$object->number};*}
	{*var name = {$object->name};*}
	{*var price = {$object->getPriceBest()};*}
	{*var param1 = {$object->getParamValueByUID('TypMontaze')};*}
	{*var param2 = {$object->getParamValueByUID('Barva')};*}
	{*var param3 = {$object->getParamValueByUID('BarvaSvetla')};*}

	{*dataLayer.push({*}
		{*'page' : {*}
			{*'type' : 'detail',*}
			{*'kind': 'product',*}
			{*'detail' : {*}
				{*'family': family,*}
				{*'products' : [*}
					{*{*}
						{*'id' : number, // unikatni ID varianty produktu*}
						{*'name' : name, // jméno produktu*}
						{*'price' : price, // bez DPH*}
						{*'brand' : family, // rodina*}
						{*'category' : param1, // typ montaze*}
						{*'colour' : param2,*}
						{*'chromaticTemperature' : param3*}
					{*}*}
				{*]*}
			{*}*}
		{*}*}
	{*});*}

		{*{if $ajax}*}
{*//			dataLayer.page.push({*}
{*//				'title': 'Svítidlo Lipo',*}
{*//				'fullPath': '/svitidlo-lipo'*}
{*//			});*}

		{*var title = {$title};*}
		{*var alias = "/"+{$object->alias};*}

		{*dataLayer.push({*}
				{*'page' : {*}
					{*'title': title,*}
					{*'fullPath': alias*}
				{*},*}
				{*'event': 'page'*}
			{*});*}
		{*{/if}*}

	{*{elseif $gtmKey == "reference"}*}
		{*var pager = {$pager};*}
		{*var family = [];*}
		{*var states = [];*}
		{*{if $filter}*}
			{*{foreach $filter as $fkey => $fi}*}
				{*{if $fkey == "families"}*}
					{*{foreach $fi as $fam}*}
						{*{if $fam->isActive}*}
							{*family.push({$fam->name});*}
						{*{/if}*}
					{*{/foreach}*}
				{*{/if}*}
				{*{if $fkey == "states"}*}
					{*{foreach $fi as $fam}*}
						{*{if $fam->isActive}*}
							{*states.push({$fam->name});*}
						{*{/if}*}
					{*{/foreach}*}
				{*{/if}*}
			{*{/foreach}*}
		{*{/if}*}
		{*dataLayer.push({*}
			{*'page' : {*}
				{*'type' : 'references.list',*}
				{*'kind': 'references',*}
				{*'references': {*}
					{*'list': {*}
						{*'number' : pager,*}
						{*'filters' : {"Family": family, "State":states}*}
					{*}*}
				{*}*}
			{*}*}
		{*});*}
	{*{elseif $gtmKey == "referenceDetail"}*}
		{*var title = {$object->name};*}
			{*dataLayer.push({*}
				{*'page' : {*}
					{*'references' : null*}
				{*}*}
			{*});		dataLayer.push({*}
			{*'page' : {*}
				{*'type' : 'references.detail',*}
				{*'kind': 'references',*}
				{*'references': {*}
					{*'detail': {*}
						{*'name' : title,*}
					{*}*}
				{*}*}
			{*}*}
		{*});*}

		{*{if $ajax}*}
			{*var alias = "/"+{$object->alias};*}
			{*dataLayer.push({*}
				{*'page' : {*}
					{*'title': title,*}
					{*'fullPath': alias*}
				{*},*}
				{*'event': 'page'*}
			{*});*}
		{*{/if}*}

	{*{elseif $gtmKey == "support"}*}
		{*dataLayer.push({*}
			{*'page' : {*}
				{*'type' : 'support',*}
				{*'kind': 'support'*}
			{*}*}
		{*});*}

	{*{elseif $gtmKey == "download"}*}
		{*dataLayer.push({*}
			{*'page' : {*}
				{*'type' : 'download',*}
				{*'kind': 'download'*}
			{*}*}
		{*});*}

	{*{elseif $gtmKey == "contact"}*}
		{*dataLayer.push({*}
			{*'page' : {*}
				{*'type' : 'contact',*}
				{*'kind': 'contact'*}
			{*}*}
		{*});*}

	{*{elseif $gtmKey == "homepage"}*}
		{*dataLayer.push({*}
			{*'page': {*}
				{*'type': 'home',*}
				{*'kind': 'home'*}
			{*}*}
		{*});*}

{*//	dataLayer.push({*}
{*//		'pageType': pagetype,*}
{*//		'visitorLoginState' : visitorLoginState,*}
{*//		'visitorId' : visitorId,*}
{*//		'pageCategory' : pageCategory*}
{*//	});*}

	{*{/if}*}

{*</script>*}
{*{/if}*}
{*{/if}*}


