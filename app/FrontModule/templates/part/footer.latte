<footer class="footer">
	<div class="row-main">
		<section class="footer__content u-pt-lg">
			<div class="footer__content__grid">
				<div n:ifcontent>
					{include $presenter->getTemplatefile('part/menu/footer.latte'), menu : $mutation->cf->menu_footer_1}
				</div>
				<div n:ifcontent>
					{include $presenter->getTemplatefile('part/menu/footer.latte'), menu : $mutation->cf->menu_footer_2}
				</div>
				<div>
					<h2 class="m-footer__header h5" n:ifcontent>{_'footer_header_3'}</h2>
					{include $presenter->getTemplatefile('part/menu/social-networks.latte'), menu : $mutation->cf->menu_social_networks, direction : 'vertical'}
					<div class="m-social-networks__list m-social-networks__list--vertical">
						<a n:href="$pages->newsletter" class="m-social-networks__link" n:if="isset($pages->newsletter)">
							{('envelope-open-text')|icon}
							<span class="m-social-networks__link-text">{_'footer_link_newsletter'}</span>
						</a>
					</div>
				</div>
				<div>
					<h2 class="m-footer__header h5" n:ifcontent>{_'footer_header_4'}</h2>
					<p n:ifcontent>
						{$pages->contact->cf->contact_page_text->text|replace:',',"\n"|breaklines|noescape}
						{if $pages->contact->cf->contact_page_map->url}
							<br>
							<a href="{$pages->contact->cf->contact_page_map->url}" target="_blank" rel="noopener noreferrer">{$pages->contact->cf->contact_page_map->text}</a>
						{/if}
					</p>
					<div class="m-social-networks">
						<ul class="m-social-networks__list m-social-networks__list--vertical">
							{ifset $pages->contact->cf->contact_page_contacts->email}
								<li class="m-social-networks__item">
									<a href="mailto:{$pages->contact->cf->contact_page_contacts->email->url}" title="{$pages->contact->cf->contact_page_contacts->email->text|noescape}" class="m-social-networks__link" target="_blank" rel="noopener noreferrer">
										{('email')|icon}
										<span class="m-social-networks__link-text">{$pages->contact->cf->contact_page_contacts->email->text|noescape}</span>
									</a>
								</li>
							{/ifset}

							{ifset $pages->contact->cf->contact_page_contacts->phoneGroup}
								<li class="m-social-networks__item">
									<span class="m-social-networks__text">{$pages->contact->cf->contact_page_contacts->phoneGroup->phoneText}</span>
									<a href="tel:{$pages->contact->cf->contact_page_contacts->phoneGroup->phone->url}" title="{$pages->contact->cf->contact_page_contacts->phoneGroup->phone->text|noescape}" class="m-social-networks__link" target="_blank" rel="noopener noreferrer">
										{('phone')|icon}
										<span class="m-social-networks__link-text">{$pages->contact->cf->contact_page_contacts->phoneGroup->phone->text|noescape}</span>
									</a>
								</li>
							{/ifset}

							{ifset $pages->contact->cf->contact_page_contacts->phoneGroupOne}
								<li class="m-social-networks__item">
									<span class="m-social-networks__text">{$pages->contact->cf->contact_page_contacts->phoneGroupOne->phoneTextOne}</span>
									<a href="tel:{$pages->contact->cf->contact_page_contacts->phoneGroupOne->phoneOne->url}" title="{$pages->contact->cf->contact_page_contacts->phoneGroupOne->phoneOne->text|noescape}" class="m-social-networks__link" target="_blank" rel="noopener noreferrer">
										{('phone')|icon}
										<span class="m-social-networks__link-text">{$pages->contact->cf->contact_page_contacts->phoneGroupOne->phoneOne->text|noescape}</span>
									</a>
								</li>
							{/ifset}

							{ifset $pages->contact->cf->contact_page_contacts->phoneGroupTwo}
								<li class="m-social-networks__item">
									<span class="m-social-networks__text">{$pages->contact->cf->contact_page_contacts->phoneGroupTwo->phoneTextTwo}</span>
									<a href="tel:{$pages->contact->cf->contact_page_contacts->phoneGroupTwo->phoneTwo->url}" title="{$pages->contact->cf->contact_page_contacts->phoneGroupTwo->phoneTwo->text|noescape}" class="m-social-networks__link" target="_blank" rel="noopener noreferrer">
										{('phone')|icon}
										<span class="m-social-networks__link-text">{$pages->contact->cf->contact_page_contacts->phoneGroupTwo->phoneTwo->text|noescape}</span>
									</a>
								</li>
							{/ifset}
						</ul>
					</div>
				</div>
			</div>
		</section>

		<section class="footer__copyright">
			<div class="b-copyright-texts u-text-content">
				<p>
					{php $year = 2022}
					&copy; {$year|copyright} {_'copyright'}
				</p>
				<p n:ifcontent><i n:ifcontent>{_'copyright_desc'}</i></p>
				<p class="u-no-print"><button type="button" class="as-link" data-cookie-open>{_'cookie_settings'}</button></p>
			</div>
		</section>
	</div>
</footer>
