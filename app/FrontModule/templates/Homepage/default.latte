{var $isHomepage = true}
{import $templates.'/part/box/carousel.latte'} {* carouselBtns *}
{varType SuperKoderi\Pages $pages}
{block content}
	{snippet content}
		{*THICKBOX*}
		{if $presenter->isAjax()}
			{include $templates.'/part/box/content.latte'}
		{else}
			{* carousel *}
			{include sectionHomepageBanner}
			{include sectionHomepageBenefits}
			{include sectionHomepageCategories}
			{include sectionHomepageAd}

			<div class="row-main u-mb-lg">
				<div class="grid">
					<div class="grid__cell size--6-12@lg">
						{include $templates.'/part/crossroad/articles.latte', crossroad: $blogCalendarEvents, page: $calendarEventsPage, pager: false, showLink: false, class: 'c-articles--hp'}
					</div>
					<div class="grid__cell size--6-12@lg">
						{include $templates.'/part/crossroad/articles.latte', crossroad: $articles, page: $actualitiesPage, pager: false, showLink: false, class: 'c-articles--hp'}
					</div>
				</div>
			</div>

			{* content *}
			<section class="row-main row-main--small" n:ifcontent>
				{include $templates.'/part/box/annot.latte'}
				{* {include $templates.'/part/crossroad/std.latte'} *}
				{include $templates.'/part/box/content.latte'}
			</section>
			{* vypisy produktu *}
{*            {include sectionHomepageProducts, content: isset($object->cf->homepage_products_section_1) ? $object->cf->homepage_products_section_1 : null}*}


			{var $link =  $presenter->link($pages->eshopVines, ['filter' => ['dials' => ['properties'=> [6660=>6660]]]])}
			{include sectionHomepageProducts,
				products: $news,
				url: $link,
				title: $translator->translate('homepage.news.title'),
				linkText: $translator->translate('homepage.news.linkName'),
			}

			{var $link =  $presenter->link($pages->eshopVines, ['filter' => ['dials' => ['properties'=> [6659=>6659]]]])}
			{include sectionHomepageProducts,
				products: $bestsellers,
				url: $link,
				title: $translator->translate('homepage.bestseller.title'),
				linkText: $translator->translate('homepage.bestseller.linkName'),
			}
			{* vypis aktualit *}
			<section class="row-main u-mb-lg" n:if="$actualities && $actualities->count()" n:ifcontent>
				{include $templates.'/part/crossroad/articles.latte', crossroad: $actualities, pager: false, heading: 'heading_actualities', linkTitle: 'link_actualities', page: $pages->blog}
			</section>
			<section class="row-main u-mb-lg" n:ifcontent>
				{include $templates.'/part/attached/images.latte'}
				{include $templates.'/part/attached/videos.latte'}
				{include $templates.'/part/attached/files.latte'}
			</section>
			<div class="row-main" n:ifcontent>
				{control newsletterForm}
			</div>
		{/if}
	{/snippet}
{/block}

{define sectionHomepageBanner}
	<div class="b-carousel b-carousel--homepage u-mb-lg"{if sizeof($object->cf->homepage_banner) > 1} data-controller="Embla" data-Embla-autoplay-value="7000" data-Embla-loop-value="true"{/if} n:if="isset($object->cf->homepage_banner)">
		<div class="b-carousel__viewport embla__viewport" data-embla-target="viewport">
			<div class="grid grid--0 grid--0 grid--scroll embla__container" n:inner-foreach="$object->cf->homepage_banner as $slide">
				<div n:class="grid__cell, embla__slide, !$iterator->first ? u-no-print">
					{var $imgAlt = isset($slide->contentHeading) ? $slide->contentHeading : ''}
					<a n:tag-if="isset($slide->page)" n:href="$slide->page" class="b-carousel__link" title="{$imgAlt}">
						<picture class="b-carousel__img" n:if="isset($slide->imageDesktop)">
							{if isset($slide->imageMobil)}
								{include $templates.'/part/core/image.latte', img: $slide->imageMobil, type: 'source', srcset: ['carousel-sm'], sizes: 'max-width: calc(750px - 1px)'}
							{elseif isset($slide->imageDesktop)}
								{include $templates.'/part/core/image.latte', img: $slide->imageDesktop, type: 'source', srcset: ['carousel-sm'], sizes: 'max-width: calc(750px - 1px)'}
							{/if}
							{include $templates.'/part/core/image.latte', img: $slide->imageDesktop, type: 'source', srcset: ['carousel'], sizes: 'min-width: calc(750px)'}
							{include $templates.'/part/core/image.latte', img: $slide->imageDesktop, loading: $iterator->first ? 'eager' : 'lazy', class: 'b-carousel__img u-d-b@md', srcset: ['carousel'], alt: $imgAlt, wrapper: false}
						</picture>
					</a>
					<div class="b-carousel__inner">
						<div class="b-carousel__content u-text-content u-text-lg" n:ifcontent>
							<h2 n:ifset="$slide->contentHeading" n:ifcontent>{$slide->contentHeading}</h2>
							<p n:ifset="$slide->contentText" n:ifcontent>{$slide->contentText}</p>
						</div>
					</div>
				</div>
			</div>
		</div>
		{include carouselBtns, count: sizeof($object->cf->homepage_banner)}
	</div>
{/define}

{define sectionHomepageBenefits}
	<section class="c-benefits u-mb-lg u-pt-sm">
		<div class="row-main">
			<div class="grid">
				{if isset($object->cf->homepage_benefits) && $object->cf->homepage_benefits}
					{foreach $object->cf->homepage_benefits as $benefit}
						<div class="grid__cell size--6-12@md size--3-12@lg">
							<div class="b-benefit">
								{if isset($benefit->icon) && $benefit->icon}
									{php $img = $benefit->icon->getSize('100-100')}
									{if $img}
										<p class="b-benefit__icon">
											<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy"/>
										</p>
									{/if}
								{/if}
								{if isset($benefit->head) && $benefit->head}
									<h3 class="b-benefit__title">{$benefit->head}</h3>
								{/if}
								{if isset($benefit->desc) && $benefit->desc}
									<p class="b-benefit__desc">{$benefit->desc}</p>
								{/if}
							</div>
						</div>
					{/foreach}
				{/if}
			</div>
		</div>
	</section>
{/define}

{define sectionHomepageCategories}
	<section class="c-categories">
		<div class="row-main">
			{if isset($object->cf->homepage_categories) && $object->cf->homepage_categories}
				<h2 class="c-categories__title">
					{_popular_categories}
				</h2>
				<div class="grid">
					{foreach $object->cf->homepage_categories as $category}
						<div class="grid__cell size--6-12@lg">
							<div class="b-hp-category">
								{if isset($category->icon) && $category->icon}
									{php $img = $category->icon->getSize('100-100')}
									{if $img}
										{if isset($category->page) && $category->page && isset($category->page->url) && $category->page->url}
											<p class="b-hp-category__main-icon">
												<a href="{$category->page->url}">
													<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy"/>
												</a>
											</p>
										{else}
											<p class="b-hp-category__main-icon">
												<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy"/>
											</p>
										{/if}
									{/if}
								{/if}
								<div class="b-hp-category__list grid">
									{foreach $category->group as $item}
										{if isset($item->page) && $item->page && isset($item->page->url) && $item->page->url}
											<a href="{$item->page->url}" class="b-hp-category__item grid__cell size--6-12">
												{if isset($item->icon) && $item->icon}
													{php $img = $item->icon->getSize('100-100')}
													{if $img}
														<p class="b-hp-category__icon">
															<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy"/>
														</p>
													{/if}
												{/if}
												{if isset($item->head) && $item->head}
													<h4 class="b-benefit__title">{$item->head}</h4>
												{/if}
											</a>
										{else}
											<div class="b-hp-category__item grid__cell size--6-12">
												{if isset($item->icon) && $item->icon}
													{php $img = $item->icon->getSize('100-100')}
													{if $img}
														<p class="b-hp-category__icon">
															<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy"/>
														</p>
													{/if}
												{/if}
												{if isset($item->head) && $item->head}
													<h4 class="b-benefit__title">{$item->head}</h4>
												{/if}
											</div>
										{/if}
									{/foreach}
								</div>
							</div>
						</div>
					{/foreach}
				</div>
			{/if}
		</div>
	</section>
{/define}

{define sectionHomepageAd}
	{if isset($object->cf->homepage_ad) && $object->cf->homepage_ad}
		{if isset($object->cf->homepage_ad->image) && $object->cf->homepage_ad->image}
			{php $img = $object->cf->homepage_ad->image->getSize('xxl')}
			{if $img}
				<div class="b-hp-ad u-mb-lg">
					<div class="row-main">
						<p class="b-hp-ad__image">
							{if isset($object->cf->homepage_ad->url) && $object->cf->homepage_ad->url}
								<a href="{$object->cf->homepage_ad->url}" target="_blank">
									<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy"/>
								</a>
							{else}
								<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy"/>
							{/if}
						</p>
					</div>
				</div>
			{/if}
		{/if}
	{/if}
{/define}

{define sectionHomepageProducts}

	{if !isset($forceLink)}
		{var $forceLink = null}
	{/if}


	<section class="row-main u-mb-lg" n:if="$products->count() > 0 ">
		<div class="c-heading-h2">
			<h2 n:if="isset($title)">{$title}</h2>
			<div class="c-heading-h2__more" n:ifcontent>
				<a href="{$url}" n:if="isset($url) && $url" class="c-heading-h2__link">
					{$linkText}
				</a>
			</div>
		</div>
		{include '../part/attached/products.latte',
			products: $products,
			heading: "", class: "",
			section: false,
			limit: 4,
			forceLink: $forceLink
		}
	</section>
{/define}
