{block content}
	{snippet content}
		{*THICKBOX*}
		{if $presenter->isAjax()}
			{include $templates.'/part/box/content.latte'}
		{else}
			{* cover & annot *}
			{capture $pageAnnot}<p><a class="btn" n:href="downloadTastingGuide!, $hash">{_link_tasting_guide}</a></p>{/capture}
			{include $templates.'/part/box/article-cover.latte', pageCover: isset($object->cf->pageCover->image) && $object->cf->pageCover->image ? $object->cf->pageCover->image, pageAnnot: $pageAnnot}

			<div class="row-main u-mt-lg" n:ifcontent>
				<div class="row-main row-main--small" n:ifcontent>
					{include $templates.'/part/crossroad/std.latte'}
					{include $templates.'/part/box/content.latte'}
				</div>
			</div>
		{/if}
	{/snippet}
{/block}