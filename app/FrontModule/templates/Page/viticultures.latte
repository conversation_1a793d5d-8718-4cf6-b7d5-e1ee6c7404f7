{varType App\Model\Parameter $viticultureParameter}

{block content}
	{snippet content}
		{* cover & annot *}
		{include $templates.'/part/box/article-cover.latte', pageCoverClass: !(isset($object->crossroad) && count($object->crossroad)) ? 'u-mb-lg' : '', pageCover: isset($object->cf->pageCover->image) && $object->cf->pageCover->image ? $object->cf->pageCover->image}

		{* podkategorie *}
		<div n:ifcontent class="section section--secondary u-mb-lg">
			<div class="row-main" n:ifcontent>
				{include $templates.'/part/crossroad/std.latte', class: ''}
			</div>
		</div>
		<div class="row-main row-main--small u-mb-lg" n:ifcontent>
			{include $templates.'/part/box/content.latte'}
			{control customContentRenderer}
		</div>
		<section class="row-main u-mb-lg" n:if="$viticultureParameter->options" n:ifcontent>
			<h2 class="u-vhide">{_'viticultures_list_heading'}</h2>
			<div class="c-branches" n:ifcontent>
				<div class="c-branches__list grid" n:inner-foreach="$viticultureParameter->options as $viticultureParameterValue" n:ifcontent>
					{var $loading = $iterator->counter > 2 ? 'lazy' : 'eager'}
					<div class="grid__cell size--6-12@md" n:ifset="$viticultureParameterValue->cf->viticultures">
						<article class="c-branches__item">
							{default $cleanFilterParam = []}
							{php $cleanFilterParam['dials'][$viticultureParameter->uid][$viticultureParameterValue->id] = $viticultureParameterValue->id}
							{capture $link}{link $pages->eshopVines, filter => $cleanFilterParam}{/capture}
							{php $link = urldecode(htmlspecialchars_decode($link))}
							<div class="c-branches__img img">
								{ifset $viticultureParameterValue->cf->viticultures->photo}
									{include $templates.'/part/core/image.latte',
										img: $viticultureParameterValue->cf->viticultures->photo,
										alt: '',
										srcset: ['branch'],
										wrapper: false
									}
								{else}
									<img src="/static/img/noimg.svg" alt="" loading="{$loading}" width="630" height="360">
								{/ifset}
								{ifset $viticultureParameterValue->cf->viticultures->image}
									{include $templates.'/part/core/image.latte',
										img: $viticultureParameterValue->cf->viticultures->image,
										alt: '',
										srcset: ['100w' => '1x', '200w' => '2x'],
										sizes: '100px',
										class: 'c-branches__logo'
									}
								{/ifset}
							</div>
							<div class="c-branches__content" n:ifset="$viticultureParameterValue->cf->viticultures->annotation">
								<h3 class="h5">{$viticultureParameterValue->value}</h3>
								{$viticultureParameterValue->cf->viticultures->annotation|noescape}
								<p><a href="{$link}" class="btn">{_'viticultures_button_link'}</a></p>
							</div>
						</article>
					</div>
				</div>
			</div>
		</section>
		<div class="row-main row-main--small u-mb-lg" n:ifcontent>
			{include $templates.'/part/attached/images.latte'}
			{include $templates.'/part/attached/videos.latte'}
			{include $templates.'/part/attached/files.latte'}
		</div>
		<div n:ifcontent class="row-main">
			{control newsletterForm}
		</div>
	{/snippet}
{/block}
