<!DOCTYPE html>
<html lang="{$mutation->langCode}" class="no-js">
	<head>
		<meta charset="utf-8">
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
		{varType App\Model\SeoLinkLocalization $seoLink}
		{if isset($seoLink) && $seoLink && $seoLink->keywords}
			<meta name="keywords" content="{$seoLink->keywords}">
		{elseif $object->keywords}
			<meta name="keywords" content="{$object->keywords}">
		{/if}
		{if isset($seoLink) && $seoLink && $seoLink->description}
			<meta name="description" content="{$seoLink->description}">
		{elseif !empty($seoFilterCatalog->description)}
			<meta name="description" content="{$seoFilterCatalog->description}">
		{elseif isset($object->description) && $object->description}
			<meta name="description" content="{$object->description}">
		{elseif isset($object->annotation) && $object->annotation}
			<meta name="description" content="{$object->annotation|texy:true}">
		{/if}

		{control robots}
		{control canonicalUrl}
		<meta name="viewport" content="width=device-width, initial-scale=1">

		<title n:snippet="title">
			{if isset($seoLink) && $seoLink && $seoLink->nameTitle}
				{$seoLink->nameTitle}
			{elseif !empty($seoFilterCatalog->title)} {* H1 po filtraci *}
				{$seoFilterCatalog->title}
			{elseif isset($object->nameTitle)}
				{$object->nameTitle}
			{/if}
			{if $object instanceOf \App\Model\CatalogTree}
				{varType App\Model\CatalogTree $object}
				{if isset($currentPage) && isset($pageCount) && $currentPage > 1}
					{_page} {$currentPage} {_from} {$pageCount}
				{/if}
			{/if}
			{if !$isHomepage} {* Přídavek za title, který se dává jen pro ne homepage stránky *}
				| {_title}
			{/if}
		</title>

		{include 'part/head/style.latte', object=>$object}
		{include 'part/head/scripts.latte', object=>$object}


		{include 'part/head/meta.latte'}
		{include 'part/head/structured_data.latte'}

		{include 'part/tracking/gtm_end_header.latte'}
		{include 'part/tracking/googleAnalytics.latte', showTop: TRUE}

		{var $scripts = [
			'https://cdn.polyfill.io/v2/polyfill.min.js?features=default,Array.prototype.includes,Object.values,Array.prototype.find',
			'/static/js/app.js?t=' . $webVersion
		] }
		{foreach $scripts as $script}
			<link rel="preload" as="script" href="{$script}">
		{/foreach}
		{*
			<link rel="dns-prefetch" href="https://www.google-analytics.com">
			<link rel="dns-prefetch" href="https://www.googletagmanager.com"> {/gtm.js}
			<link rel="preconnect" href="https://www.google.com" crossorigin>
			<link rel="preconnect" href="https://www.youtube.com" crossorigin>
			<link rel="preconnect" href="https://connect.facebook.net" crossorigin>
			<link rel="preconnect" href="https://static.doubleclick.net" crossorigin>
			<link rel="preconnect" href="https://client.crisp.chat" crossorigin>
		*}
	</head>
	<script>var dataLayer = dataLayer || []; // Google Tag Manager</script>
	<body data-controller="Naja Modal">
		{include 'part/tracking/googleAnalytics.latte', showBottom: TRUE}
		{include 'part/menu/accessibility.latte'}

		{snippetArea header}
			{include 'part/header.latte'}
		{/snippetArea}

		{snippet contentModal}
			{control modal}

			<main id="main" class="main">
				{block content}{/block}
			</main>
		{/snippet}

		{include './part/box/last-visited.latte'}

		{include 'part/footer.latte'}

		{include 'part/box/cookie.latte'}

		<div class="body-loader__loader"></div>

		{include 'part/tracking/gtm_end.latte'}

		{foreach $scripts as $script}
			<script src="{$script}"></script>
		{/foreach}
		<script>
			App.run({
				apiKey: {$googleApiKey},
				assetsUrl: '/static/',
			});
		</script>
	</body>
</html>
