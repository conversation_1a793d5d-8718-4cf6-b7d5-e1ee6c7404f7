<div class="b-filters__group">
	<p class="b-filters__title">
		{_'flag_'.$filterItem->name}:
	</p>
	<ul class="b-filters__list">
		{foreach $filterItem->activeValues as $value}
			{if isset($filterItem->entity)}
				{capture $unit}{_'pname_unit_'.$filterItem->entity->id}{/capture}
			{else}
				{var $unit = null}
			{/if}
			{capture $link}{link 'this', filter => $value->followingFilterParameters}{/capture}
			{php $link = urldecode(htmlspecialchars_decode($link))}

			<li class="b-filters__item">
				<a href="{$link}" class="b-filters__remove" data-naja data-naja-loader="body"{if $linkSeo->hasNofollow($object, ['filter' => $value->followingFilterParameters])} rel="nofollow"{/if}>
					{if $filterItem->name == 'color'}
						{$variantParameterValues['color'][$value->value]->value}
					{else}
						{$value->inputValue}{if $unit} {$unit}{/if}
					{/if}
				</a>
			</li>
		{/foreach}
	</ul>
</div>
