<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Model\Orm;
use App\Model\Sentry\SentryLogger;
use Contributte\Monolog\LoggerManager;
use InvalidArgumentException;
use Psr\Log\LoggerInterface;
use SuperKoderi\ConfigService;

abstract class BaseErpConnector implements IErpConnector
{

	protected Orm $orm;

	protected ConfigService $configService;

	protected array $config = [];

	public const ACTION_GET_PRODUCTS = 'getProducts';
	public const ACTION_PUT_ORDER = 'putOrder';

	protected LoggerInterface $infoLogger;

	protected LoggerInterface $errorLogger;

	protected SentryLogger $sentryLogger;

	public function __construct(string $name, Orm $orm, ConfigService $configService, LoggerManager $loggerManager, SentryLogger $sentryLogger)
	{
		$this->orm = $orm;
		$this->configService = $configService;
		$connectorsConfig = $this->configService->getParam('erp', 'connectors');
		if (!isset($connectorsConfig[$name])) {
			throw new InvalidArgumentException('Invalid connector name. See erp.neon - section parameters.config.erp.connectors for the list of connectors.');
		}

		$this->config = $connectorsConfig[$name];

		$this->infoLogger = $loggerManager->get('erpInfo');
		$this->errorLogger = $loggerManager->get('erpError');
		$this->sentryLogger = $sentryLogger;
	}

}
