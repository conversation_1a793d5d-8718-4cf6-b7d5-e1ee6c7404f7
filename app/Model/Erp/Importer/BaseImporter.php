<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\Erp\Connector\IErpConnector;
use App\Model\ImportCache;
use App\Model\Orm;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use SuperKoderi\ConfigService;

class BaseImporter
{

	protected Orm $orm;

	protected IErpConnector $connector;

	public const ACTION_REMOVE = 'remove';

	protected array $config;

	public function __construct(IErpConnector $connector, Orm $orm, ConfigService $configService)
	{
		$this->orm = $orm;
		$this->connector = $connector;
		$this->config = $configService->getParam('erp');
	}

	protected function baseImportItem(string $extId, string $action, string $type): ImportCache
	{
		$response = $this->connector->getData($action, ['extId' => $extId]);
		$item = $response->data;
		$this->adjustItem($item);

		$importCacheItem = $this->orm->importCache->create($type, $item, $extId);
		$this->orm->importCache->setStatus([$importCacheItem->id], ImportCache::STATUS_READY);

		return $importCacheItem;
	}

	protected function adjustItem(ArrayHash $item): void
	{
		//to be implemented in child classes
	}

}
