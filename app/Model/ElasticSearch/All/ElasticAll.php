<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\All;

use App\Model\Author;
use App\Model\Blog;
use App\Model\BlogTag;
use App\Model\CatalogTree;
use App\Model\CommonTree;
use App\Model\ElasticSearch\Entity;
use App\Model\HpTree;
use App\Model\Mutation;
use App\Model\Product;
use App\Model\SeoLinkLocalization;
use App\PostType\Modal\Model\Orm\ModalLocalization;
use LogicException;

/**
 * @param Convertor[] $convertors
 */
class ElasticAll implements Entity
{
	const TYPE_VARIANT = 'variant';
	const TYPE_PRODUCT = 'product';
	const TYPE_TREE = 'tree';
	const TYPE_BLOG = 'blog';
	const TYPE_SEOLINK = 'seolink';
	const TYPE_MODAL = 'modal';

	public function __construct(
		private object $object,
		private array $convertors = [],
	)
	{
	}

	function getId(): string
	{
		assert($this->object instanceof Product
			|| $this->object instanceof Blog
			|| $this->object instanceof CommonTree
			|| $this->object instanceof CatalogTree
			|| $this->object instanceof SeoLinkLocalization
			|| $this->object instanceof ModalLocalization

		);
		$class = get_class($this->object);
		return match ($class) {
			Product::class => self::TYPE_PRODUCT . '-' . $this->object->id,
			SeoLinkLocalization::class => self::TYPE_SEOLINK . '-' . $this->object->id,
			ModalLocalization::class => self::TYPE_MODAL . '-' . $this->object->id,
			Blog::class => self::TYPE_BLOG . '-' . $this->object->id,
			CommonTree::class, CatalogTree::class => self::TYPE_TREE . '-' . $this->object->id,
			default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
		};
	}

	function getData(Mutation $mutation): array
	{
		$convertedData = [];
		foreach ($this->convertors as $convertor) {
			$convertedData[] = $convertor->convert($this->object);
		}
		return array_merge(...$convertedData);
	}

}
