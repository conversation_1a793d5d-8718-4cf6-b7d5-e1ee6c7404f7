<?php

declare(strict_types=1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\Model\SeoLinkLocalization;
use function assert;

final class SeoLinkData implements Convertor
{

	public function convert(object $seoLink): array
	{
		assert($seoLink instanceof SeoLinkLocalization);
		return [
			'id' => $seoLink->id,
			'isSystemPage' => false,
			'type' => 'seoLink',
			'langCode' => $seoLink->mutation->langCode,
			'name' => $seoLink->name . ' (' . $seoLink->getParent()->internalName . ')',
			'nameTitle' => $seoLink->nameTitle,
			'description' => $seoLink->description,
			'annotation' => '',
			'kind' => 'seoLink',
		];
	}

}
