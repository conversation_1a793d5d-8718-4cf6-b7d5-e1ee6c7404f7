<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\CatalogTreeModel;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm;
use App\Model\Product;
use JetBrains\PhpStorm\ArrayShape;

class CategoryData implements Convertor
{

	public function __construct(
		private Orm $orm,
		private CatalogTreeModel $catalogTreeModel,
	)
	{
	}

	function convert(Product $product): array
	{
		$mutation = $product->getMutation();

		$allCategories = $this->catalogTreeModel->getAllCatalogCategories($product, $mutation);
		$allCategoriesIds = array_values(array_map(function ($category) {
			return $category->id;
		}, $allCategories));

		$allPublicCategories = $this->orm->tree->findByIds($allCategoriesIds)
			->findBy($this->orm->tree->getPublicOnlyWhereParams())
		;

		$data = [
			'path' => $allPublicCategories->fetchPairs(null, 'id'),
			'categories' => $product->attachCategories->findBy(['rootId' => $mutation->rootId])->fetchPairs(null, 'id'),
		];

		if ($mainCategory = $product->mainCategory) {
			$data['mainCategory'] = $mainCategory->id;
		}
		return $data;
	}
}
