<?php declare(strict_types = 1);

namespace App\Model;

use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nextras\Orm\Mapper\IMapper;
use Nextras\Orm\Repository\IDependencyProvider;
use Nextras\Orm\Repository\Repository;
use SuperKoderi\hasPublicParameterTrait;

/**
 * @method Banner getById($id)
 */
final class BannerRepository extends Repository
{

	use hasPublicParameterTrait;

	private Cache $cache;

	public function __construct(IMapper $mapper, ?IDependencyProvider $dependencyProvider, Storage $cacheStorage)
	{
		$this->cache = new Cache($cacheStorage);
		parent::__construct($mapper, $dependencyProvider);
	}


	public static function getEntityClassNames(): array
	{
		return [Banner::class];
	}


	public function onFlush(array $persitedEntities, array $removedEntities): void
	{
		$this->cache->clean([
			Cache::TAGS => ['banners'],
		]);

		parent::onFlush($persitedEntities, $removedEntities); // TODO: Change the autogenerated stub
	}


	public function getPublicOnlyWhereParams(): array
	{
		$ret = [
			'public' => 1,
		];

		$now = $this->getNowDateTime();

		$ret['publicFrom<='] = $now;
		$ret['publicTo>='] = $now;

		return $ret;
	}

}
