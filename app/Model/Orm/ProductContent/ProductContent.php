<?php

namespace App\Model;
use \Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int $id {primary}
 * @property Product $product {m:1 Product::$contents}
 * @property Image|null $image {1:1 Image, isMain=true, oneSided=true}
 * @property string|null $name
 * @property string|null $count
 * @property string|null $text
 * @property DateTimeImmutable|null $createdTime
 * @property int $created
 * @property int $sort
 *
 */
class ProductContent extends \Nextras\Orm\Entity\Entity
{
	use \SuperKoderi\hasTranslatorTrait;

//	protected function getterText($text)
//	{
//		$replace = '<span class=\"adlink\">' . $this->translator->translate('Reaguje na ') . '[<a href=\"#comment\\1\">\\1</a>]:</span>';
//		return preg_replace('/\[([[:digit:]]+)\]:/i', $replace, $text);
//	}
}