<?php


namespace App\Model;

use App\Model\Functions\LikeFilterFunction;

/**
 * @method Place getById($id)
 * @method Place[] findByLocation($lat, $lon, $limit = 4)
 */
final class PlaceRepository extends \Nextras\Orm\Repository\Repository
{
	static function getEntityClassNames(): array
	{
		return [Place::class];
	}

    protected function createCollectionFunction(string $name)
    {
        if ($name === LikeFilterFunction::class) {
            return new LikeFilterFunction();
        } else {
            return parent::createCollectionFunction($name);
        }
    }

}
