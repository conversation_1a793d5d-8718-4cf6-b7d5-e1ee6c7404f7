<?php


namespace App\Model;



use App\Model\Functions\LikeFilterFunction;

/**
 * @method OrderItem getById($id)
 * @method float sumPriceDPH($orders)
 * @method float sumPrice($orders)
 */
final class OrderItemRepository extends \Nextras\Orm\Repository\Repository
{
	static function getEntityClassNames(): array
	{
		return [OrderItem::class];
	}


	protected function createCollectionFunction(string $name)
	{
		if ($name === LikeFilterFunction::class) {
			return new LikeFilterFunction();
		} else {
			return parent::createCollectionFunction($name);
		}
	}

}