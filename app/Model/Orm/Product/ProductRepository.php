<?php declare(strict_types = 1);

namespace App\Model;

use Nette\Caching\Cache;
use Nette\Caching\Storage;
use <PERSON>ras\Dbal\Result\Result;
use Nextras\Dbal\Result\Row;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\IMapper;
use Nextras\Orm\Repository\IDependencyProvider;
use Nextras\Orm\Repository\Repository;
use SuperKoderi\hasPublicParameterTrait;
use SuperKoderi\HasSimpleSave;

/**
 * @method Product|null getById($id)
 * @method ICollection|Product[] findByPageId($id)
 * @method ICollection|Product[] findPromoted()
 * @method ICollection|Product[] findByFilter($filter)
 * @method ICollection|Product[] findRandom()
 * @method ICollection|Product[] findBySetpartsById($id)
 * @method ICollection|Product[] findSetBySetpartId($id)
 * @method ICollection|Product[] addToSet($set, $product, $sort)
 * @method ICollection|Product[] removeFromSet($set, $product)
 * @method ICollection|Product[] findByParameterValuesUnion(Discount $discount)
 * @method ICollection|Product[] findByParameterValue(string $parameterUid, string $value)
 *
 * @method ICollection|Product[] searchByName($q, array $excluded = NULL, Mutation $mutation = null)
 *
 * @method ICollection|Tree[] findMainProductsInRelations(Product $attachedProduct, string $type)
 * @method ICollection|Tree[] findAttachedProductsInRelations(Product $mainProduct, string $type)
 *
 * @method ICollection|Product[] findProductsInTreeProductRelations(Tree $tree, string $type)
 * @method ICollection|Product[] addToPages($page, $product, $sort)
 * @method ICollection|Product[] removeFromPages($page, $product)
 * @method ICollection|Product[] updateToPages($page, $product, $sort)
 *
 * @method Result addParameterValue(Product|Row $product, ParameterValue $parameterValue)
 * @method Result removeParameterValue(Product|Row $product, ParameterValue $parameterValue)
 * @method Result removeParameter(Product $product, Parameter $parameter)
 * @method Result removeMissingParameterValuesIds(Product $product, array $selectedParameterValuesIds)
 *
 * @method ICollection|Product[] findSimilarProducts(Product $product, int $count, array $excludedIds, array $parameterUIDs)
 * @method ICollection|Product[] findFilteredProducts($productIds)
 * @method ICollection|Product[] findBestseller(Mutation $mutations, Tree $category = null)
 * @method Product save(?Product $entity, array $data)
 * @method void disableIsNewFlag(int $productId)
 *
 *
 * @method Product|null getBySarze(string $sarze)
 * @method array findAllIds(?int $limit)
 * @method void resetSoldCount()
 *
 *
 */
final class ProductRepository extends Repository
{

	use hasPublicParameterTrait;
	use HasSimpleSave;


	public static function getEntityClassNames(): array
	{
		return [Product::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		/** @var Orm $orm */
		$orm = $this->getModel();
		$ret = [
			'productLocalizations->public' => 1,
			'productLocalizations->mutation' => $orm->getMutation(),
		];

		$now = $this->getNowDateTime();

		$ret['publicFrom<='] = $now;
		$ret['publicTo>='] = $now;

		return $ret;
	}


}
