<?php

namespace App\Model;

use App\Tratis\Orm\hasCamelCase;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Mapper;

class MutationTransportsMapper extends Mapper
{
	use hasCamelCase;

	protected $tableName = 'mutation_transports';

	public function findByFilter(?ArrayHash $filter): ICollection
	{
		$builder = $this->builder()->select('t.*')->from($this->tableName, 't');

		if (!empty($filter->mutation)) {
			$builder->andWhere('mutationId = %i', $filter->mutation);
		}

		return $this->toCollection($builder);
	}

}
