<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property int $sort
 * @property string $type {enum self::TYPE_*}
 *
 *
 * RELATIONS
 * @property Product $mainProduct {m:1 Product::$productProducts}
 * @property Product $attachedProduct {m:1 Product::$attachedProducts}
 *
 *
 * VIRTUAL
 */
class ProductProduct extends Entity
{

	public const TYPE_NORMAL = 'normal';
	public const TYPE_PRESENT = 'present';
	public const TYPE_SET = 'set';
	public const TYPE_ACCESSORY = 'accessory';
	public const TYPE_SIMILAR = 'similar';

}
