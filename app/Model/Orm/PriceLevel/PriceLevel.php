<?php

namespace App\Model;

use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $type {enum self::TYPE_*}
 * @property string $name
 *
 * RELATIONS
 * @property ProductVariantPrice[]|OneHasMany $prices {1:m ProductVariantPrice::$priceLevel, cascade=[persist, remove]}
 * @property Discount[]|ManyHasMany $discounts {m:m Discount::$priceLevels}
 * @property User[]|OneHasMany $users {1:m User::$priceLevel, cascade=[persist, remove]}
 *
 * VIRTUALS
 * @property string $cleanLevel {virtual}
 *
 */
class PriceLevel extends Entity
{
	const DEFAULT_ID = 1;


	const TYPE_DEFAULT = 'default';
	const TYPE_PRICE_10 = 'price10';
	const TYPE_PRICE_11 = 'price11';
	const TYPE_PRICE_12 = 'price12';
	const TYPE_PRICE_13 = 'price13';
	const TYPE_PRICE_14 = 'price14';
	const TYPE_PRICE_15 = 'price15';
    const TYPE_PRICE_ACTION = 'action';

    const TYPE_PRICE_ACTION_ID = 8;


    protected function getterCleanLevel(): string
	{
		if ($this->type === self::TYPE_DEFAULT) {
			return '0';
		} else {
			return str_replace('price', '', $this->type);
		}
	}
}
