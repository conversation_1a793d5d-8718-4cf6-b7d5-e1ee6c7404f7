<?php

namespace App\Model;

use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\HasMany;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasCustomFieldTrait;
use SuperKoderi\hasTranslatorTrait;

/**
 * @property int $id {primary}
 * @property string $email
 * @property string|null $password
 * @property string|null $role {default self::ROLE_USER} {enum self::ROLE_*}
 * @property string|null $firstname {default ''}
 * @property string|null $lastname {default ''}
 * @property string|null $phone {default ''}
 * @property string $street {default ''}
 * @property string $zip {default ''}
 * @property string $city {default ''}
 * @property string $ic {default ''}
 * @property string $dic {default ''}
 * @property string $company {default ''}
 * @property string $infosCode {default ''}
 * @property DateTimeImmutable|null $createdTime {default now}
 * @property DateTimeImmutable|null $editedTime
 * @property DateTimeImmutable|null $lastLogin
 * @property string|null $customAddressJson
 * @property int $orderCount {default 0}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 *
 * RELATIONS
 * @property ProductReview[]|OneHasMany $reviews {1:m ProductReview::$user}
 * @property UserImage[]|OneHasMany $images {1:m UserImage::$user, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property UserHash[]|OneHasMany $hashes {1:m UserHash::$user}
 * @property Order[]|OneHasMany $orders {1:m Order::$user}
 * @property Mutation[]|ManyHasMany $mutations {m:m Mutation::$users, isMain=true}
 * @property PriceLevel $priceLevel {m:1 PriceLevel::$users}
 * @property State|null $state {m:1 State::$users}
 * @property UserMutation[]|OneHasMany $userMutations {1:m UserMutation::$user, cascade=[persist, remove]}
 * @property UserFavoriteProduct[]|OneHasMany $userFavoriteProducts {1:m UserFavoriteProduct::$user, orderBy=[createdAt=ASC]}
 * @property ProductWatchdog[]|OneHasMany $watchdogs {1:m ProductWatchdog::$user, orderBy=[createdAt=DESC]}
 *
 *
 * VIRTUAL
 * @property-read string $name {virtual}
 * @property-read UserImage|null $firstImage {virtual}
 * @property-read Mutation|null $mutation {virtual}
 * @property array|null $customAddress {virtual}
 * @property ArrayHash|null $cf {virtual}
 *
 */
class User extends \Nextras\Orm\Entity\Entity
{
	use hasConfigServiceTrait;
	use hasTranslatorTrait;
	use hasCustomFieldTrait;

	public const ROLE_USER = 'user';
	public const ROLE_ADMIN = 'admin';
	public const ROLE_DEVELOPER = 'developer';


	protected function getterCustomAddress(): mixed
	{
		assert($this->getMetadata()->hasProperty('customAddressJson'));
		if ($this->customAddressJson === null) {
			return null;
		}

		$data = \json_decode($this->customAddressJson, flags: \JSON_THROW_ON_ERROR);
		return $data;
	}


	protected function setterCustomAddress(mixed $customAddress): void
	{
		assert($this->getMetadata()->hasProperty('customAddressJson'));
		$this->customAddressJson = \json_encode($customAddress, \JSON_THROW_ON_ERROR);
	}

	protected function getterFirstImage(): ?UserImage
	{
//		return $this->imagesOrigin->toCollection()->fetch();
		return $this->images->toCollection()->fetch();
	}

	protected function getterMutation(): ?Mutation
	{
		return $this->mutations->toCollection()->fetch();
	}


	protected function getterName(): string
	{
		return trim($this->firstname . ' ' . $this->lastname);
	}
}
