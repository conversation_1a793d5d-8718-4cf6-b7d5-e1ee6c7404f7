<?php declare(strict_types = 1);

namespace App\Model;

use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $type {enum self::TYPE_*}
 * @property ArrayHash $data {container JsonArrayHashContainer}
 * @property string $status {enum self::STATUS_*} {default self::STATUS_NEW}
 * @property string|null $extId
 * @property DateTimeImmutable $createdTime {default now}
 * @property DateTimeImmutable|null $importedTime {default null}
 * @property string|null $message
 *
 *
 * RELATIONS
 *
 * VIRTUAL
 */
class ImportCache extends Entity
{

	public const STATUS_NEW = 'new';
	public const STATUS_READY = 'ready';
	public const STATUS_IMPORTED = 'imported';
	public const STATUS_ERROR = 'error';
	public const STATUS_PROCESSING = 'processing';
	public const STATUS_SKIPPED = 'skipped';

	//max. length of type is 4 characters
	public const TYPE_PRODUCT = 'prod';
	public const TYPE_PARAM = 'par';
	public const TYPE_PRICE = 'pric';
	public const TYPE_PHOTO = 'phot';
	public const TYPE_STOCK = 'stoc';
	public const TYPE_ORDER = 'ord';

}
