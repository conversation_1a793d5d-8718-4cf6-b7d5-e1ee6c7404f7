<?php declare(strict_types = 1);

namespace App\Model;

use App\Tratis\Orm\hasCamelCase;
use Nextras\Dbal\QueryException;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class ImportCacheMapper extends DbalMapper
{

	use hasCamelCase;

	/**
	 * @param int[]  $ids
	 * @throws QueryException
	 */
	public function setStatus(array $ids, string $status): void
	{
		if (count($ids) > 0) {
			$sql = 'UPDATE `import_cache` SET `status` = %s WHERE `id` IN %i[]';
			$this->connection->query($sql, $status, $ids);
		}
	}

	/**
	 * @return int - number of deleted rows
	 * @throws QueryException
	 */
	public function removeOldItems(DateTimeImmutable $olderThan, string $type, array $statuses): int
	{
		//leave the last item
		$builder = $this->builder()->select('id')->from('import_cache')->where('`type` = %s', $type)->orderBy('id DESC');
		if (($lastRow = $this->connection->queryArgs($builder->getQuerySql(), $builder->getQueryParameters())->fetch()) === null) {
			return 0;
		}

		$sql = 'DELETE FROM `import_cache` WHERE `type` = %s AND `createdTime` <= %dt AND `status` IN %s[] AND `id` != %i';
		$this->connection->query($sql, $type, $olderThan, $statuses, $lastRow->id);

		return $this->connection->getAffectedRows();
	}

}
