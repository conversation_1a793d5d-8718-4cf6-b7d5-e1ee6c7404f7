<?php declare(strict_types = 1);

namespace App\Model;

use Exception;
use Nextras\Orm\Entity\Entity;
use <PERSON>Kode<PERSON>\hasBlogRepositoryTrait;
use SuperKoderi\hasProductLocalizationRepositoryTrait;
use SuperKoderi\hasProductRepositoryTrait;
use SuperKoderi\hasTreeRepositoryTrait;

/**
 * @property int $id {primary}
 * @property string $alias
 * @property string $module {enum self::MODULE_*}
 * @property int $referenceId
 *
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$aliases}
 *
 *
 * VIRTUALS
 * @property-read Tree|Product $parent {virtual}
 * @property-read string $name {virtual}
 */
final class Alias extends Entity
{

	use hasTreeRepositoryTrait;
	use hasProductRepositoryTrait;
	use hasBlogRepositoryTrait;
	use hasProductLocalizationRepositoryTrait;

	public const MODULE_TREE = 'tree';
	public const MODULE_PRODUCT = 'productLocalization';
	public const MODULE_BLOG = 'blog';
	public const MODULE_SEOLINK = 'seoLinkLocalization';

	public const MODULE_MODAL = 'modalLocalization';


	private SeoLinkLocalizationRepository $seoLinkLocalizationRepository;

	public function injectRepo(SeoLinkLocalizationRepository $seoLinkLocalizationRepository): void
	{
		$this->seoLinkLocalizationRepository = $seoLinkLocalizationRepository;
	}

	public function __toString(): string
	{
		return $this->isPersisted() ? $this->alias : '';
	}


	/**
	 * @return Product|Tree|null
	 * @throws Exception
	 */
	protected function getterParent()
	{
		$repository = $this->module . 'Repository';
		$parent = $this->$repository->getById($this->referenceId);

		if ($parent instanceof ProductLocalization) {
			return $parent->product;
		} else {
			return $parent;
		}
	}


	protected function getterName(): string
	{
		return $this->alias;
	}

}
