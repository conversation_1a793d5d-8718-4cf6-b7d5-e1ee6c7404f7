<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property Product $product {m:1 Product::$watchdogs}
 * @property User|null $user {m:1 User::$watchdogs}
 * @property string $email
 * @property string $type {enum self::TYPE_*}
 * @property float|null $targetPrice
 * @property bool $active {default true}
 * @property DateTimeImmutable $createdAt {default now}
 * @property DateTimeImmutable|null $notifiedAt
 * @property DateTimeImmutable|null $lastCheckedAt
 *
 * VIRTUAL
 */
class ProductWatchdog extends Entity
{
    public const TYPE_IN_STOCK = 'in_stock';
    public const TYPE_PRICE_DROP = 'price_drop';
    public const TYPE_PRICE_UNDER = 'price_under';
}
