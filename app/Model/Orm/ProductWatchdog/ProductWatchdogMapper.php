<?php declare(strict_types = 1);

namespace App\Model;

use App\Tratis\Orm\hasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class ProductWatchdogMapper extends DbalMapper
{
    use hasCamelCase;

    protected $tableName = 'product_watchdog';

    public function findActiveByProduct(Product $product): ICollection
    {
        return $this->toCollection(
            $this->builder()
                ->andWhere('productId = %i', $product->id)
                ->andWhere('active = 1')
        );
    }

    public function findActiveByType(string $type): ICollection
    {
        return $this->toCollection(
            $this->builder()
                ->andWhere('type = %s', $type)
                ->andWhere('active = 1')
        );
    }

    public function findForPriceCheck(): ICollection
    {
        return $this->toCollection(
            $this->builder()
                ->andWhere('active = 1')
                ->andWhere('type IN %s[]', [ProductWatchdog::TYPE_PRICE_DROP, ProductWatchdog::TYPE_PRICE_UNDER])
        );
    }

    public function findForStockCheck(): ICollection
    {
        return $this->toCollection(
            $this->builder()
                ->andWhere('active = 1')
                ->andWhere('type = %s', ProductWatchdog::TYPE_IN_STOCK)
        );
    }
}
