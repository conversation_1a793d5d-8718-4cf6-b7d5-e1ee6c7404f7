<?php


namespace App\Model;

/**
 * @method ProductReview getById($id)
 * @method array getStatistic($id)
 * @method array getProductForReviewFormOrder($id)
 */
final class ProductReviewRepository extends \Nextras\Orm\Repository\Repository
{
	static function getEntityClassNames(): array
	{
		return [ProductReview::class];
	}


	public function getMainReview(Product $product, int $userId): ?ProductReview
	{
		return $this->getBy(['product' => $product->id, 'userId' => $userId]);
	}

}
