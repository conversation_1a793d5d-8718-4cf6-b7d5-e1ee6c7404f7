<?php
/**
 * Created by PhpStorm.
 * User: vojta
 * Date: 24.11.17
 * Time: 21:02
 */

namespace App\Model;


use Nextras\Dbal\Utils\DateTimeImmutable;

class ProductReviewModel
{
	function __construct(
		private Orm $orm,
	) {}


	public function updateReview(ProductReview $productReview, ?int $stars, ?string $text): void
	{
		$this->orm->productReview->persistAndFlush($productReview);
		$productReview->stars = $stars;
		$productReview->text = $text;
		$this->recalculateCache($productReview->product);
	}

	public function addReview(Product $product, ?string $name, ?DateTimeImmutable $date, ?int $stars, ?string $text, ?string $email, int $isWebMaster, ?int $userId, bool $isMain = false): void
	{
		$productReview = new ProductReview();
		$productReview->product = $product;
		$productReview->name = $name;
		$productReview->email = $email;
		$productReview->stars = $stars;
		$productReview->date = $date;
		$productReview->text = $text;
		$productReview->isWebMaster = $isWebMaster;
		$productReview->isMain = (int) $isMain;

		if ($userId) {
			$productReview->userId = $userId;
		}

		$this->orm->productReview->persistAndFlush($productReview);
		$this->recalculateCache($product);

	}


	private function recalculateCache(Product $product): void
	{
		$mapper = $this->orm->productReview->getMapper();
		\assert($mapper instanceof ProductReviewMapper);
		$statistic = $mapper->getStatistic($product)->fetch();

		$product->reviewAverage = $statistic->average;
		$this->orm->product->persistAndFlush($product);
	}


}
