<?php

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int $id {primary}
 * @property Product $product {m:1 Product::$reviews}
 * @property User|null $user {m:1 User::$reviews}
 * @property int $isWebMaster {default 0}
 * @property int $isMain {default 0}
 * @property string|null $name
 * @property string|null $email
 * @property string|null $text
 * @property int|null $stars
 * @property DateTimeImmutable|null $date {default 'now'}
 *
 * @property int|null $userId {virtual}
 * @property string|null $dateString {virtual}
 */
class ProductReview extends \Nextras\Orm\Entity\Entity
{

	protected function getterDateString(): ?string
	{
		return $this->date?->format("Y-m-d H:i:s");
	}

	protected function getterUserId(): ?int
	{
		return $this->user?->id;
	}

}
