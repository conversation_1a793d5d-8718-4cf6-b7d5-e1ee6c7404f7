<?php
/**
 * Created by PhpStorm.
 * User: vojta
 * Date: 08/11/2018
 * Time: 15:24
 */

namespace App\Model;


use App\Infrastructure\Latte\Filters;
use SuperKoderi\TranslatorDB;

class VoucherCodeModel
{
	public const ERROR_MIN_PRICE = 'error_voucher_bad_using_min_order';  // chyba pokud je vyse objednavky niszi nez je potreba
	public const ERROR_NO_EXISTS = 'error_voucher_no_exists'; // chyba pokud zadany voucher neexistuje
	public const ERROR_EXPIRED = 'error_voucher_expired'; // chyba pokud voucher uz casove vyprsel
	public const ERROR_COMBINATION = 'error_voucher_combination'; // chyba pokud jiz byl voucher jednou pouzit
	public const ERROR_USED = 'error_voucher_used'; // chyba pokud voucher nlze pouzit v kombinaci s jinym voucherem v kosiku
	public const ERROR_BAD_USING_PRODUCT = 'error_voucher_bad_using_product'; // Zadavný slevový poukaz je platný pouze pro produkty
	public const ERROR_BAD_USING_SERVICE = 'error_voucher_bad_using_service'; // Zadavný slevový poukaz je platný pouze pro služby


	public function __construct(
		private Orm $orm,
		private TranslatorDB $translatorDB,
	) {}


	public function isValid(VoucherCode $voucherCode, \SuperKoderi\Basket $basket, bool $inBasket = false): bool
	{
		if (!$voucherCode->voucher->isValid) {
			throw new VoucherCodeException(self::ERROR_EXPIRED);
		}

		if ($voucherCode->voucher->kind == "normal") {
			// validTo
			if (
				$voucherCode->voucher->publicTo !== NULL && $voucherCode->voucher->publicTo < new \DateTime ||
				$voucherCode->voucher->publicFrom !== NULL && $voucherCode->voucher->publicFrom > new \DateTime
			) {
				throw new VoucherCodeException(self::ERROR_EXPIRED);
			}
		} else {
			// produktovy poukaz
			// u voucheru ktere jsou generovane pri nakupu (poukazky), hlidame platnost kodu, ne voucheru
			$r = new \DateTime();
			$r->sub(new \DateInterval('P1Y'));
			if ($voucherCode->createdTime < $r) {
				// ubehl uz vic jak rok
				throw new VoucherCodeException(self::ERROR_EXPIRED);
			}
		}

		if ($voucherCode->isUsed) {
			throw new VoucherCodeException(self::ERROR_USED);
		}

		if ($voucherCode->voucher->minPriceOrder > 0) {
			// na co muze byt pouzity
			if ($voucherCode->voucher->application == "product") {
				$priceBasket = $basket->getProductsPriceDPH();
			} elseif ($voucherCode->voucher->application == "service") {
				$priceBasket = $basket->getServicesPriceDPH();
			} else { // all
				$priceBasket = $basket->getItemsPriceDPH();
			}

			if ($priceBasket < $voucherCode->voucher->minPriceOrder) {
				$msg = TranslatorDB::ALREADY_TRANSLATED_MARKER . $this->translatorDB->translate(self::ERROR_MIN_PRICE);
				throw new VoucherCodeException(str_replace('%minOrderPrice%', Filters::priceFormat($voucherCode->voucher->minPriceOrder), $msg));
			}
		}

		// kontrola pouziti poukazu
		// mam v kosiku jen produkty -> vlozim voucher na služby -> info
		// mam v kosiku jen sluzby -> vlozim voucher na produkty -> info
		if ($voucherCode->voucher->application == "product") {
			$priceBasket = $basket->getProductsPriceDPH();
			if ($priceBasket == 0) {
				throw new VoucherCodeException(self::ERROR_BAD_USING_PRODUCT);
			}
		} elseif ($voucherCode->voucher->application == "service") {
			$priceBasket = $basket->getServicesPriceDPH();
			if ($priceBasket == 0) {
				throw new VoucherCodeException(self::ERROR_BAD_USING_SERVICE);
			}
		}


		// jiz je pouzit
		if ($inBasket) {
			if ($basket->getVoucherCodes()->count() > 1 && !$basket->isAllowMoreVouchers()) {
				throw new VoucherCodeException(self::ERROR_COMBINATION);// v kosiku je jiz jeden kod, dalsi nemuzu pridat
			}
		} else {
			foreach ($basket->getVoucherCodes() as $voucherCodeInBasket) {
				if ($voucherCode == $voucherCodeInBasket) {
					throw new VoucherCodeException(self::ERROR_USED);// kod je jiz pouzit
				} elseif (!$basket->isAllowMoreVouchers()) {
					throw new VoucherCodeException(self::ERROR_COMBINATION);// v kosiku je jiz jeden kod, dalsi nemuzu pridat
				}
			}
		}

		return TRUE;
	}


	/**
	 * @param VoucherCode[] $vouchers
	 */
	public function deactivateCodes(iterable $vouchers): void
	{
		if (!$vouchers) {
			return;
		}

		foreach ($vouchers as $voucherCode) {

			if ($voucherCode->voucher->reuse != 1) {
				$voucherCode->isUsed = 1;

				$this->orm->persistAndFlush($voucherCode);
			}
		}
	}

	public function addCodes(Voucher $voucher, int $count, string $prefix = ''): void
	{
		$c = 0;
		do {
			$c++;
			$this->addCode($voucher, FALSE, TRUE, null, $prefix);
		} while ($c < $count);

		$this->orm->flush();
	}


	public function addCode(Voucher $voucher, bool $return = TRUE, bool $commit = false, float $newPrice = NULL, string $prefix = ''): ?VoucherCode
	{
		$newCode = new VoucherCode();
		$newCode->voucher = $voucher;
		$newCode->code = $prefix . $this->generateCode($voucher);
		if ($newPrice > 0) {
			$newCode->specialPriceDPH = $newPrice;
		}

		if ($commit) {
			$this->orm->voucherCode->persistAndFlush($newCode);
		} else {
			$this->orm->voucherCode->persist($newCode);
		}

		if ($return) {
			return $newCode;
		} else {
			return null;
		}
	}

	public function addExactCode(Voucher $voucher, string $code): void
	{
		$oldCode = $this->orm->voucherCode->getBy([
			'code' => $code,
			'voucher->mutation->id' => $voucher->mutation->id
		]);

		if ($oldCode === null) {
			$newCode = new VoucherCode();
			$newCode->voucher = $voucher;
			$newCode->code = $code;

			$this->orm->voucherCode->persistAndFlush($newCode);
		}

	}


	private function generateCode(Voucher $voucher): string
	{
		$mutation = $voucher->mutation;

		do {
			$value = \Nette\Utils\Random::generate(6, 'A-Z0-9');
		} while ($this->orm->voucherCode->getBy([
			'code' => $value,
			'voucher->mutation->id' => $mutation->id
		]));
		return $value;
	}


	public function getVoucherInfo(string $code, OrderItem $orderItem, bool $full = FALSE): \stdClass
	{
		$code = trim($code);
		$voucherCode =  $this->orm->voucherCode->getBy(['code' => $code]);
		if ($voucherCode->specialPriceDPH) {
			$price = $voucherCode->specialPriceDPH;
		} else {
			$price = $voucherCode->voucher->discountAmount;
		}
		$validTo = $voucherCode->createdTime->add(new \DateInterval('P1Y'));

		$voucherInfo = new \stdClass();
		$voucherInfo->orderNumber = $orderItem->order->number;
		$voucherInfo->name = $orderItem->name;
		$voucherInfo->code = $code;
		$voucherInfo->value = $price;
		$voucherInfo->createdObject = $voucherCode->createdTime;
		$voucherInfo->validToObject = $voucherCode->createdTime;
		$voucherInfo->validTo = $validTo->format("j. n. Y");
		$voucherInfo->mutation = $orderItem->order->mutation;

		if ($full) {
			$voucherInfo->voucherProduct = NULL;
			// ID skoleni - treeID
//			$treeID = (int) $orderItem->variantString;
//			if ($treeID) {
//				$class = $this->orm->tree->getById($treeID);
//				if ($class->productAttachedId) {
//					$voucherInfo->voucherProduct = $this->orm->product->getById($class->productAttachedId);
//					bd($voucherInfo->voucherProduct);
//				}
//			}

			if ($orderItem->product) {
				$voucherInfo->voucherProduct = $orderItem->product;
			}
		}

		return $voucherInfo;
	}

}


class VoucherCodeException extends \Exception
{

}
