<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\hasFormDefaultDataTrait;

/**
 * @property int $id {primary}
 * @property string $name {default ''}
 * @property string $internalName {default ''}
 * @property string $kind {enum self::KIND_*} {default self::KIND_NORMAL}
 * @property string $type {enum self::TYPE_*} {default self::TYPE_AMOUNT}
 * @property string $application {enum self::APPLICATION_*} {default self::APPLICATION_ALL}
 * @property float|null $discountAmount
 * @property int|null $discountPercent
 * @property float|null $minPriceOrder
 * @property int $reuse {default 0}
 * @property int $public {default 0}
 *
 * @property int|null $created
 * @property int|null $edited
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property DateTimeImmutable|null $publicFrom {default 'now'}
 * @property DateTimeImmutable|null $createdTime {default 'now'}
 * @property DateTimeImmutable|null $editedTime
 *
 *
 *
 * RELATIONS
 * @property VoucherCode[]|OneHasMany $codes {1:m VoucherCode::$voucher, cascade=[persist, remove]}
 * @property Mutation $mutation {m:1 Mutation::$vouchers}
 * @property ProductLocalization[]|OneHasMany $productLocalizations {1:m ProductLocalization::$voucher}
 *
 *
 * VIRTUAL
 * @property-read bool $isValid {virtual}
 */
class Voucher extends Entity
{

	use hasFormDefaultDataTrait;

	public const KIND_NORMAL = 'normal';
	public const KIND_PRODUCT = 'product';

	public const TYPE_AMOUNT = 'amount';
	public const TYPE_PERCENT = 'percent';

	public const APPLICATION_PRODUCT = 'product';
	public const APPLICATION_SERVICE = 'service';
	public const APPLICATION_ALL = 'all';

	protected function getterIsValid(): bool
	{
		$now = new DateTimeImmutable();
		return $this->public && $this->publicFrom < $now && $now < $this->publicTo;
	}

}
