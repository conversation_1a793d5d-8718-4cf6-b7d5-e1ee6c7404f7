{if $cfObject->getCfScheme()}

	{var $icon = $templates.'/part/icons/list.svg'}

	{var $props = [
		title: $title,
		id: $containerName . '_' .$itemName,
		icon: $icon,
		variant: 'main',
		open: true,
		classes: ['u-mb-xxs'],
	]}

	{php $headerItems[] = [href: '#'.$itemName, icon: $icon, tooltip: $title, linkType: 'toggle']}
	{formContainer $containerName}
		{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
			{block content}
				<div class="b-std u-mb-sm">
					<h3 class="b-std__title title"></h3>
					<div class="b-std__content"
						 data-controller="CustomFields"
						 data-action="CustomField:updateValue->CustomFields#updateValue CustomFieldImage:updateValue->CustomFields#updateValue CustomFieldFile:updateValue->CustomFields#updateValue CustomFieldList:addListItem->CustomFields#addListItem CustomFieldList:removeListItem->CustomFields#removeListItem CustomFieldList:updateListOrder->CustomFields#updateListOrder"
						 data-customfields-scheme-value="{$cfObject->getCfSchemeJson()}"
						 data-customfields-values-value="{$cfObject->getCfContent()}"
						 data-customfields-uploadurl-value="{$fileUploadLink}"
					>
						<div data-customfields-target="content"></div>
						<input n:name="{$itemName}" type="hidden" data-customfields-target="values">
					</div>
				</div>
			{/block}
		{/embed}
	{/formContainer}
{/if}

