<?php declare(strict_types = 1);

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;
use SuperKoderi\hasPublicParameterTrait;

/**
 * @method Blog getById($id)
 * @method Blog[]|ICollection searchByName(string $q, array $excluded = [])
 * @method Blog[]|ICollection findByIdInPathString(CommonTree $commonTree)
 * @method Blog[]|ICollection findFiltered(array $ids)
 *
 * @method array findAllIds(?int $limit)
 * @method array findAllIdsInMutation(Mutation $mutation, ?int $limit)
 */
final class BlogRepository extends Repository
{

	use hasPublicParameterTrait;

	public static function getEntityClassNames(): array
	{
		return [Blog::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		$ret = [
			'public' => 1,
		];

		$now = $this->getNowDateTime();

		$ret['publicFrom<='] = $now;
		$ret['publicTo>='] = $now;

		return $ret;
	}

}
