<?php declare(strict_types = 1);

namespace SuperKoderi\Admin\Components\Blog\Form;

use App\Model\Blog;
use App\Model\Mutation;
use App\Model\User;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Http\IRequest;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use SuperKoderi\CustomField\SuggestUrls;
use SuperKoderi\hasConfigServiceTrait;
use SuperKoderi\hasOrmTrait;
use SuperKoderi\LinkFactory;
use SuperKoderi\Translator;
use SuperKoderi\TranslatorDB;

/**
 * @property-read DefaultTemplate $template
 */
class Form extends Control
{

	use hasOrmTrait;
	use hasConfigServiceTrait;

	/** @var array */
	private array $postData;

	/** @var Blog */
	private Blog $blog;

	/** @var User */
	private User $userEntity;

	private SuggestUrls $urls;

	/** @var LinkFactory */
	private LinkFactory $linkFactory;

	/** @var Translator */
	private Translator $translator;

	/** @var TranslatorDB */
	private TranslatorDB $translatorDB;

	/** @var Builder */
	private Builder $formBuilder;

	/** @var Success */
	private Success $formSuccess;


	public function __construct(
		Blog $blog,
		User $userEntity,
		SuggestUrls $urls,
		LinkFactory $linkFactory,
		Translator $translator,
		TranslatorDB $translatorDB,
		Builder $formBuilder,
		Success $formSuccess
	)
	{
		$this->blog = $blog;
		$this->userEntity = $userEntity;
		$this->urls = $urls;
		$this->linkFactory = $linkFactory;
		$this->translator = $translator;
		$this->translatorDB = $translatorDB;
		$this->formBuilder = $formBuilder;
		$this->formSuccess = $formSuccess;

		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$method = $this->getPresenter()->request->getMethod();

		if ($method === IRequest::POST) {
			$this->postData = $this->getPresenter()->request->getPost();
		} else {
			$this->postData = [];
		}
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);

		$template->blog = $this->blog;
		$template->mutation = $this->blog->mutation;
		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link('File:upload');
		$template->linksToFront = $this->linkFactory->linkTranslateToNette($this->blog, ['show' => 1, 'mutation' => $this->blog->mutation]);

		$template->config = $this->configService->getParams();
		$template->userEntity = $this->userEntity;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}


	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setTranslator($this->translator);

		$this->formBuilder->build($form, $this->blog, $this->postData);

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(\Nette\Application\UI\Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(\Nette\Application\UI\Form $form, ArrayHash $values): void
	{
		$this->formSuccess->execute($form, $this->blog, $this->userEntity, $values);
		$this->presenter->redirect('edit', ['id' => $this->blog->id]);
	}


	public function handleDelete(): void
	{
		// TODO delete from ES
		$this->orm->blog->removeAndFlush($this->blog);
		$this->presenter->redirect('default');
	}

}


interface IFormFactory
{

	/**
	 * @return Form
	 */
	function create(Blog $blog, User $userEntity);

}
