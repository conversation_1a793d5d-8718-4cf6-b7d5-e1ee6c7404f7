
{*
<div class="u-mb-sm">
	<h2 class="b-std__title title">
		Simultánní pohled
	</h2>
	<p class="u-mb-xs" n:foreach="$otherProductTags as $otherProductTag">
		<a href="{plink ProductTag:edit, id=>$productTag->id, otherId=>$otherProductTag->id}" class="btn btn--full btn--grey">
			<span class="btn__text item-icon">
				<span class="item-icon__icon icon">
					{include $templates.'/part/icons/columns.svg'}
				</span>
				<span class="item-icon__text">
					Zobrazit s <span class="tag">{$otherProductTag->mutation->langCode}</span> verzi
				</span>
			</span>
		</a>
	</p>
</div>
*}

<div class="u-mb-sm" >
	<h2 class="b-std__title title">
		Jazykové verze
	</h2>
	{if $otherProductTags->count()}
		<p class="u-mb-xs" n:foreach="$otherProductTags as $otherProductTag">
			<a href="{plink ProductTag:edit, id=>$otherProductTag->id}" class="btn btn--full btn--grey">
				<span class="btn__text item-icon">
					<span class="item-icon__icon icon">
						{include $templates.'/part/icons/globe-europe.svg'}
					</span>
					<span class="item-icon__text">
						Editovat <span class="tag">{$otherProductTag->mutation->langCode}</span> verzi
					</span>
				</span>
			</a>
		</p>
	{/if}

	<p class="u-mb-xs">
		<button type="button" class="btn btn--full btn--grey" data-controller="Toggle" data-action="Toggle#changeClass" data-toggle-target-value="#overlay-addLang" data-toggle-target-class-value="is-visible">
			<span class="btn__text item-icon">
				<span class="item-icon__icon icon">
					{include $templates.'/part/icons/plus.svg'}
				</span>
				<span class="item-icon__text">
					Duplikovat
				</span>
			</span>
		</button>
	</p>
</div>
