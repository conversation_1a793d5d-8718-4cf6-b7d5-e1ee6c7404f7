<div class="u-hide">
	{foreach $mutations as $mutation}


		{var $url = $urls['searchMutationPage']->toArray()}
		{php $url['params']['mutationId'] = $mutation->id}
		{php $url['params']['templates'] = ['Catalog:default']}

		{var $categoryContainer = $form['localizations'][$mutation->id]['categories']['newItemMarker']}
		<div data-Templates-target="categoryItem" data-mutationid="{$mutation->id}">


			{include $templates.'/part/box/list-item.latte',
			props: [
				data: [
					controller: 'RemoveItem SuggestInp',
					removeitem-target: 'item',
					suggestinp-target: 'wrapper',
					suggestinp-url-value: Nette\Utils\Json::encode($url)
			],
				inps: [
					[
						placeholder: 'Zadejte název kategorie',
						input: $categoryContainer['name'],
						data: [
							suggestinp-target: 'input',

						]
					],
					[
						input: $categoryContainer['id'],
						data: [
							suggestinp-target: 'idInput',
						],
						classes: 'u-hide',
						type: 'hidden'
					]

				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						]
					]
				],
				dragdrop: true
			]
			}
		</div>



	{/foreach}


	{var $url = $urls['searchParameterValue']->toArray()}
	{php $url['params']['uids'] = ['raze', 'select']}

	{include $templates.'/part/core/formTargetItem.latte', props: [
		itemTargetName: 'parametersValuesItem',
		formContainer: $form['parametersValues']['newItemMarker'],
		listPlaceholder: 'Zadejte hodnotu parametru',
		listSearchUrl: $url
	]}


	{var $url = $urls['searchVariants']->toArray()}
	{include $templates.'/part/core/formTargetItem.latte', props: [
		itemTargetName: 'variantsItem',
		formContainer: $form['variants']['newItemMarker'],
		listPlaceholder: 'Zadejte jméno produktu',
		listSearchUrl: $url
	]}


	{var $url = $urls['searchProduct']->toArray()}
	{include $templates.'/part/core/formTargetItem.latte', props: [
		itemTargetName: 'productsItem',
		formContainer: $form['products']['newItemMarker'],
		listPlaceholder: 'Zadejte jméno produktu',
		listSearchUrl: $url
	]}
</div>
