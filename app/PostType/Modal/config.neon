cf:
	fields:
		base:
			type: group
			label: "Obsah"
			items:
				title:
					type: text
					label: "Titulek"
				content:
					type: tinymce
					label: "Obsah"

	templates:
		"modalLocalization": [@cf.base]


application:
	mapping:
		Modal: App\PostType\Modal\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Modal: modal
		ModalLocalization: modalLocalization
	config:
		modal:
			paging: 9
services:
	## MODAL

	- App\PostType\Modal\AdminModule\Components\Form\Builder
	- App\PostType\Modal\AdminModule\Components\Form\Handler
	- App\PostType\Modal\Model\ModalLocalizationFacade
	- App\PostType\Modal\AdminModule\Components\Form\FormFactory
