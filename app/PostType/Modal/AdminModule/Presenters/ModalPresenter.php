<?php declare(strict_types = 1);

namespace AdminModule;


use App\Model\User;
use App\PostType\Modal\AdminModule\Components\Form\Form;
use App\PostType\Modal\AdminModule\Components\Form\FormFactory;
use App\PostType\Modal\Model\ModalLocalizationFacade;
use App\PostType\Modal\Model\Orm\ModalLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;

final class ModalPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'modal';

	private ModalLocalization $modalLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $modalFormFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly ModalLocalizationFacade $modalLocalizationFacade,
	)
	{
		parent::__construct();
	}

	public function actionEdit(int $id): void
	{
		$modalLocalization = $this->orm->modalLocalization->getById($id);
		if ($modalLocalization === null) {
			$this->redirect('default');
		}

		$this->modalLocalization = $modalLocalization;
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->modalLocalization->findAll());
	}


	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->modalFormFactory->create($this->modalLocalization, $userEntity);
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(
			entity: null,
			entityLocalizationFacade:
			$this->modalLocalizationFacade
		);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
