{var $anchorName = 'content'}
{var $icon = $templates . '/part/icons/align-left.svg'}
{var $title = 'Obsah'}

{var $props = [
	title: $title,
	id: $anchorName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}


{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
	{block content}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['parent']['internalName'],
			classesLabel: ['title'],
		]}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['localization']['name'],
			classesLabel: ['title'],
			dataInp: [
				controller: 'ProductTitle',
				action: 'input->ProductTitle#changeTitle blur->ProductTitle#generateAlias',
				producttitle-lang-value: $mutation->langCode,
			]
		]}
	{/block}
{/embed}
