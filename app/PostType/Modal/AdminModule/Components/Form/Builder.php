<?php declare(strict_types = 1);

namespace App\PostType\Modal\AdminModule\Components\Form;

use App\PostType\Modal\Model\Orm\ModalLocalization;
use Nette\Application\UI\Form;

final class Builder
{

	public function __construct(
		private readonly \App\PostType\Core\AdminModule\Components\Form\Builder $coreBuilder
	)
	{
	}

	public function build(Form $form, ModalLocalization $modalLocalization): void
	{
		$form->addCheckbox('isMain', 'isMain')->setDefaultValue($modalLocalization->isMain);

		$this->coreBuilder->addLocalization($form, $modalLocalization);
		$this->coreBuilder->addPublish($form, $modalLocalization);
		$this->coreBuilder->addValidity($form, $modalLocalization);
		$this->coreBuilder->addRoutable($form, $modalLocalization);
		$this->coreBuilder->addParent($form, $modalLocalization->getParent());

		$this->coreBuilder->addButtons($form);
	}

}
