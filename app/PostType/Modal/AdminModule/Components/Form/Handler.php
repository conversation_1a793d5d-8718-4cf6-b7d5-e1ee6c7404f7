<?php declare(strict_types = 1);

namespace App\PostType\Modal\AdminModule\Components\Form;

use App\Model\Orm;
use App\Model\User;
use App\PostType\Modal\Model\Orm\ModalLocalization;
use App\PostType\Modal\AdminModule\Components\Form\FormData\BaseFormData;
use Nette\Utils\ArrayHash;

final class Handler
{

	public function __construct(
		private readonly Orm $orm,
		private readonly \App\PostType\Core\AdminModule\Components\Form\Handler $coreHandler,
	)
	{
	}


	public function handle(ModalLocalization $modalLocalization, User $user, BaseFormData $data, ArrayHash $postData): void
	{
		$this->coreHandler->handleLocalization($modalLocalization, $data->localization);
		$this->coreHandler->handleParent($modalLocalization->getParent(), $data->parent);
		$this->coreHandler->handleValidity($modalLocalization, $data->validity);
		$this->coreHandler->handlePublish($modalLocalization, $data->publish);
		$this->coreHandler->handleRoutable($modalLocalization, $data->routable);

		$this->orm->modalLocalization->persistAndFlush($modalLocalization);
	}

}
