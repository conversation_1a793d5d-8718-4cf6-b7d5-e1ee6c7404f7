<?php declare(strict_types = 1);

namespace App\PostType\Modal\AdminModule\Components\Form\FormData;

use App\PostType\Core\AdminModule\Components\Form\FormData\LocalizationFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\ParentFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\PublishFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\RoutableFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\ValidityFormData;

class BaseFormData
{

	public bool $isMain;

	public ParentFormData $parent;

	public RoutableFormData $routable;

	public PublishFormData $publish;

	public ValidityFormData $validity;

	public LocalizationFormData $localization;

}
