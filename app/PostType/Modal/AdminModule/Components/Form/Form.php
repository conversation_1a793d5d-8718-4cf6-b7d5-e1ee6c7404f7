<?php declare(strict_types = 1);

namespace App\PostType\Modal\AdminModule\Components\Form;

use App\Model\Mutation;
use App\Model\Orm;
use App\Model\User;
use App\PostType\Modal\AdminModule\Components\Form\FormData\BaseFormData;
use App\PostType\Modal\Model\Orm\ModalLocalization;
use Closure;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Http\IRequest;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;
use SuperKoderi\ConfigService;
use SuperKoderi\CustomField\SuggestUrls;
use SuperKoderi\LinkFactory;
use SuperKoderi\Translator;
use SuperKoderi\TranslatorDB;

class Form extends Control
{

	/** @var ICollection|Mutation[] */
	private ICollection $mutations;

	/** @var array|mixed|null */
	private mixed $postData;

	public function __construct(
		private readonly ModalLocalization $modalLocalization,
		private readonly User $userEntity,
		private readonly SuggestUrls $urls,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly Builder $formBuilder,
		private readonly Handler $formHandler,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}


	private function init(): void
	{
		$this->mutations = $this->orm->mutation->findAll();
		$method = $this->getPresenter()->request->getMethod();
		if ($method === IRequest::POST) {
			$this->postData = (array) $this->getPresenter()->request->getPost();
		} else {
			$this->postData = [];
		}
	}


	public function render(): void
	{
		/** @var DefaultTemplate $template */
		$template = $this->getTemplate();
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('corePartsDirectory', \App\PostType\Core\AdminModule\Components\Form\Form::TEMPLATE_PARTS_DIR);

		$template->parent = $this->modalLocalization->getParent();
		$template->entityLocalization = $this->modalLocalization;
		$template->mutation = $this->modalLocalization->mutation;
		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link(':Admin:File:upload');
		$template->linksToFront = [];
		$template->headerItems = new \ArrayIterator();

		$template->otherMutations = $this->modalLocalization->modal->localizations->toCollection()->findBy(['mutation!=' => $this->modalLocalization->mutation]);
		$activeMutationLangCodes = [];
		foreach ($this->modalLocalization->modal->localizations as $localization) {
			$activeMutationLangCodes[] = $localization->mutation->langCode;
		}

		$template->missingMutations = $this->mutations->findBy(['langCode!=' => $activeMutationLangCodes]);

		$template->config = $this->configService->getParams();
		$template->userEntity = $this->userEntity;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}


	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$from = new \Nette\Application\UI\Form();
		$from->setMappedType(BaseFormData::class);

		$this->formBuilder->build($from, $this->modalLocalization);

		$from->setTranslator($this->translator);
		$from->onSuccess[] = [$this, 'formSucceeded'];
		$from->onError[] = [$this, 'formError'];

		return $from;
	}


	public function formError(\Nette\Application\UI\Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		$this->formHandler->handle($this->modalLocalization, $this->userEntity, $data, ArrayHash::from($this->postData));

		$this->presenter->redirect('edit', ['id' => $this->modalLocalization->id]);
	}


	public function handleDelete(): void
	{
		$entity = $this->modalLocalization;
		assert($entity instanceof IEntity);
		$this->orm->modalLocalization->removeAndFlush($entity);
		$this->presenter->redirect('default');
	}

}
