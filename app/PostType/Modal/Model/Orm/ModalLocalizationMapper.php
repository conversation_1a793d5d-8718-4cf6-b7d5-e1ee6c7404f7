<?php

declare(strict_types=1);

namespace App\PostType\Modal\Model\Orm;

use App\Model\Mutation;
use App\Tratis\Orm\hasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class ModalLocalizationMapper extends DbalMapper
{

	use hasCamelCase;

	protected $tableName = 'modal_localization';

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('ml.id')
			->from($this->tableName, 'ml')
			->andWhere('ml.mutationId = %i', $mutation->id)
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

	public function findPublishedInMutationAndState(Mutation $mutation, array $closedModalIds, ?int $limit = null): ICollection
	{
		$now = $this->getNowDateTime();

		$builder = $this->builder()->select('ml.*')
			->from($this->tableName, 'ml')
			->andWhere('ml.public = 1')
			->andWhere('ml.publicFrom <= %dt', $now)
			->andWhere('ml.publicTo >= %dt', $now)
			->andWhere('ml.mutationId = %i', $mutation->id);

		if ($closedModalIds !== []) {
			$builder->andWhere('ml.id NOT IN %i[]', $closedModalIds);
		}

		$builder->limitBy($limit);

		return $this->toCollection($builder);
	}

	private function getNowDateTime(): DateTimeImmutable
	{
		$now = new DateTimeImmutable();
		return $now->setTime((int) $now->format('H'), (int) $now->format('i'), 0, 0);
	}
}
