<?php

declare(strict_types=1);

namespace App\PostType\Modal\Model\Orm;

use App\Model\JsonContainer;
use App\Model\BaseEntity;
use App\Model\Mutation;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\OneHasMany;
use SuperKoderi\hasCustomFieldTrait;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property ModalLocalization[]|OneHasMany $localizations {1:M ModalLocalization::$modal}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class Modal extends BaseEntity implements ParentEntity
{

	use hasCustomFieldTrait;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): ModalLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof ModalLocalization);
		return $localization;
	}

}
