<?php declare(strict_types = 1);

namespace App\PostType\Modal\Model\Orm;

use App\Model\Mutation;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;
use SuperKoderi\hasPublicParameterTrait;

/**
 * @method ModalLocalization getById($id)
 * @method ModalLocalization[]|ICollection searchByName(string $q, array $excluded)
 * @method ModalLocalization[]|ICollection findRandom()
 * @method array findAllIds(?int $limit)
 * @method ModalLocalization[]|ICollection findByExactOrder(array $ids)
 *
 */
final class ModalLocalizationRepository extends Repository
{

	use hasPublicParameterTrait;

	public static function getEntityClassNames(): array
	{
		return [ModalLocalization::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof ModalLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

	public function findPublishedInMutationAndState(Mutation $mutation, array $closedModalIds, ?int $limit = null): ICollection
	{
		$mapper = $this->mapper;
		assert($mapper instanceof ModalLocalizationMapper);

		return $mapper->findPublishedInMutationAndState(mutation: $mutation, closedModalIds: $closedModalIds, limit: $limit);
	}

}
