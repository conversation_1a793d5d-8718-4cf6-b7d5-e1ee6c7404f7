<?php declare(strict_types = 1);

namespace App\Console\Elastic;

use App\Model\EsIndex;
use App\Model\EsIndexFacade;
use App\Model\EsIndexRepository;
use App\Model\Mutation;
use Nette\Utils\Strings;
use App\Model\ElasticSearch\IndexModel;
use SuperKoderi\MutationsHolder;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

final class CreateCommand extends Command
{
	public function __construct(
		private IndexModel $indexModel,
		private MutationsHolder $mutationsHolder,
		private readonly EsIndexFacade $esIndexFacade,
		private readonly EsIndexRepository $esIndexRepository,
	)
	{
		parent::__construct();
	}


	protected function configure(): void
	{
		$possibleTypes = EsIndex::getConstsByPrefix('TYPE_');
		$possibleMutationCodes = Mutation::getConstsByPrefix('CODE_');

		$this->setName('elastic:index:create')
			->addOption('populate', 'p',InputOption::VALUE_NONE, 'Populate index with data')
			->addOption('switch', 's',InputOption::VALUE_NONE, 'Mark index as active')
			->addOption('clean', 'c',InputOption::VALUE_NONE, 'Remove old index')
			->addArgument('inputType',
				InputArgument::OPTIONAL,
				sprintf('ES index type [%s]', implode(',', $possibleTypes)),
				'*')
			->addArgument('inputMutationCode',
				InputArgument::OPTIONAL,
				sprintf('ES index mutation  [%s]', implode(',', $possibleMutationCodes)),
				'*')
			->setDescription('Create elastic indexes');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$populate = $input->getOption('populate');
		$switch = $input->getOption('switch');
		$clean = $input->getOption('clean');

		$newIndexIds = [];

		[$inputTypes, $inputMutationCodes] = $this->getInputs($input);

		foreach ($inputMutationCodes as $inputMutationCode) {
			$mutation = $this->mutationsHolder->getMutationByLangCode($inputMutationCode);
			foreach ($inputTypes as $inputType) {
				if ($inputType !== EsIndex::TYPE_ALL) {
					$esIndex = $this->indexModel->createIndex($inputType, $mutation);
					$newIndexIds[] = $esIndex->id;
					if ($populate) {
						$this->esIndexFacade->fill(esIndex: $esIndex, autoSwitch: $switch);
					}
				}
			}
		}

		if (in_array(EsIndex::TYPE_ALL, $inputTypes)) {
			$esIndex = $this->indexModel->createIndex(EsIndex::TYPE_ALL, $this->mutationsHolder->getDefault());
			$newIndexIds[] = $esIndex->id;
			if ($populate) {
				$this->esIndexFacade->fill(esIndex: $esIndex, autoSwitch: $switch);
			}
		}

		if ($clean) {

			$conditions = [
				'active' => 0
			];
			if ($newIndexIds !== []) {
				$conditions['id!='] = $newIndexIds;
			}

			foreach ($this->esIndexRepository->findBy($conditions) as $esIndex) {
				$this->esIndexFacade->delete($esIndex);
			}
		}

		$output->writeLn('DONE');

		return self::SUCCESS;
	}

	/**
	 * @param InputInterface $input
	 * @return array
	 */
	protected function getInputs(InputInterface $input): array
	{
		$inputType = Strings::lower($input->getArgument('inputType'));
		$inputMutationCode = Strings::lower($input->getArgument('inputMutationCode'));


		$possibleTypes = EsIndex::getConstsByPrefix('TYPE_');
		$possibleMutationCodes = Mutation::getConstsByPrefix('CODE_');

		if ($inputType === '*') {
			$inputType = $possibleTypes;
		} else {
			$inputType = array_filter(
				explode(',', $inputType),
				function ($item) use ($possibleTypes) {
					if (in_array($item, $possibleTypes)) {
						return Strings::lower($item);
					}
					return false;
				}
			);
		}

		if ($inputMutationCode === '*') {
			$inputMutationCode = $possibleMutationCodes;
		} else {
			$inputMutationCode = array_filter(
				explode(',', $inputMutationCode),
				function ($item) use ($possibleMutationCodes) {
					if (in_array($item, $possibleMutationCodes)) {
						return Strings::lower($item);
					}
					return false;
				}
			);
		}
		return array($inputType, $inputMutationCode);
	}


}
