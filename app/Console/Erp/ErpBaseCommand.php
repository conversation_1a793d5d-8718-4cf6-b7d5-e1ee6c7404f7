<?php declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Exception\LoggedException;
use App\Model\Orm;
use App\Model\OrmCleaner;
use Exception;
use Nextras\Dbal\Utils\DateTimeImmutable;
use RuntimeException;
use SuperKoderi\ConfigService;
use SuperKoderi\MutationHolder;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Tracy\Debugger;

abstract class ErpBaseCommand extends Command
{

	use LockableTrait;

	public const PRODUCT_LOCK = 'product_lock';
	public const PRODUCT_PROCESS_LOCK = 'product_process_lock';


	protected static $defaultName = 'erp:foo';

	protected const COMMAND_RUN_TIMER = 'commandRun';
	protected const COMMAND_BATCH_TIMER = 'commandBatch';

	protected int $limit = PHP_INT_MAX;

	protected int $batchLimit = 2000;

	protected int $processed = 0;

	protected array $config;

	public function __construct(protected ConfigService $configService, protected Orm $orm, protected OrmCleaner $ormCleaner, protected MutationHolder $mutationHolder)
	{
		parent::__construct();
		$this->config = $this->configService->getParam('erp');
		$this->mutationHolder->setMutation($this->orm->mutation->getDefault());
	}

	protected function configure(): void
	{
		$this->setDescription('ERP Commands base');
		$this->addOption('limit', 'l', InputOption::VALUE_OPTIONAL, 'Maximal number of items to be processed');
		$this->addOption('batchLimit', 'b', InputOption::VALUE_OPTIONAL, 'Maximal number of items to be processed in one batch');
	}

	/**
	 * @throws RuntimeException
	 */
	protected function checkLock(string $name): void
	{
		if (!$this->lock($name)) {
			throw new RuntimeException('The command is already running in another process.');
		}
	}

	protected function writeResult(array $res, OutputInterface $output): void
	{
		foreach ($res as $key => $value) {
			if (is_array($value)) {
				$value = implode(', ', $value);
			}

			$output->writeln($key . ': ' . $value);
		}
	}

	protected function startTimer(string $timer): void
	{
		Debugger::timer($timer);
	}

	protected function printTime(string $timer, OutputInterface $output): void
	{
		$output->writeln('Time: ' . number_format(Debugger::timer($timer), 3, '.', ' ') . ' s');
	}

	protected function beginExecution(string $lock, string $message, OutputInterface $output, InputInterface $input): void
	{
		$this->startTimer(self::COMMAND_RUN_TIMER);
		$output->writeln($message);
		$this->checkLock($lock);

		$this->initLimits($input);
		$output->writeln('Limit: ' . $this->limit . ', Batch limit: ' . $this->batchLimit);
	}

	protected function endExecution(OutputInterface $output): void
	{
		$this->orm->flush();
		$output->writeln('---');
		$this->printTime(self::COMMAND_RUN_TIMER, $output);
		$output->writeln('DONE');
	}

	protected function processBatches(OutputInterface $output): void
	{
		$batchNumber = 0;
		$this->startTimer(self::COMMAND_BATCH_TIMER);
		try {
			$res = $this->processBatch($output, $batchNumber);
			$this->orm->flush();
			$this->ormCleaner->safeClear();
			$output->writeln("\nBatch " . ($batchNumber + 1) . ':');
			$this->writeResult($res, $output);

			$processed = $res['Count'] ?? 0;
			$this->processed += $processed;
			if (isset($res['terminate']) && $res['terminate'] === true) {
				return;
			}
		} catch (LoggedException $e) {
			$output->writeln('Exception: ' . $e->getMessage());
			return;
		}

		$this->printTime(self::COMMAND_BATCH_TIMER, $output);
		$this->batchLimit = min($this->batchLimit, $this->limit - $this->processed);
	}

	/**
	 * Note that ORM is cleared after each batch.
	 *
	 * @return mixed[] - any report data, should contain 'Count' - number of processed items, when the 'Count' == 0, the loop ends
	 */
	protected function processBatch(OutputInterface $output, int $batchNumber): array
	{
		return ['Count' => 0];
	}

	protected function clean(OutputInterface $output): void
	{
		$output->writeln("\nCleaning");
		$res = $this->processCleaning($output);
		$this->orm->flush();
		$this->writeResult($res, $output);
	}

	/**
	 * @return mixed[] - any report data
	 */
	protected function processCleaning(OutputInterface $output): array
	{
		//should be implemented in child classes
		return [];
	}

	/**
	 * @throws Exception
	 */
	protected function getLastImportTimeValue(string $uid, InputInterface $input): DateTimeImmutable
	{
		if (($fromOption = $input->getOption('from')) !== null) {
			return new DateTimeImmutable($fromOption);
		} else {
			if (($lastImportConfig = $this->orm->config->getByUid($uid)) === null || $lastImportConfig->value === '') {
				throw new RuntimeException($uid . ' DB configuration missing');
			}

			return new DateTimeImmutable($lastImportConfig->value);
		}
	}

	protected function setLastImportTime(string $uid, DateTimeImmutable $time): void
	{
		if (($lastImportConfig = $this->orm->config->getByUid($uid)) === null) {
			throw new RuntimeException($uid . ' DB configuration missing');
		}

		$this->orm->config->save($lastImportConfig, ['value' => $time->format('c')]);
	}

	protected function initLimits(InputInterface $input): void
	{
		if (($limit = $input->getOption('limit')) !== null) {
			$this->limit = (int) $limit;
		}

		if (($batchLimit = $input->getOption('batchLimit')) !== null) {
			$this->batchLimit = (int) $batchLimit;
		}

		$this->batchLimit = min($this->batchLimit, $this->limit);
	}

}
