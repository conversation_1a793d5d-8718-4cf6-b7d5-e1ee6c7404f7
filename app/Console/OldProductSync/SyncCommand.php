<?php declare(strict_types = 1);

namespace App\Console;

use App\Model\Orm;
use App\Model\PriceLevel;
use App\Model\ProductTree;
use Nette\Utils\Strings;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Utils\DateTimeImmutable;
use SuperKoderi\ConfigService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class SyncCommand extends Command
{
	use LockableTrait;

	private Connection $zbDbal;
	/**
	 * @var \App\Model\PriceLevel[]
	 */
	private ?array $priceLevel = null;
	private DateTimeImmutable $now;
	private DateTimeImmutable $now100;

	public function __construct(
		private readonly Connection $dbal,
		private readonly ConfigService $configService,
		private readonly Orm $orm,
	)
	{
		parent::__construct(null);
	}


	protected static $defaultName = 'sync';

	protected function configure(): void
	{
		$this->setDescription('Send new orders into infos via mssql function');
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
//		if (!$this->lock()) {
//			throw new Exception('The command is already running in another process.');
//		}

		$this->zbDbal = new Connection($this->configService->get('bzDb'));
		$this->runCommand($output);
		$output->writeln('DONE');

		return self::SUCCESS;
	}


	private function runCommand(OutputInterface $output): void
	{

		$this->now100 = new DateTimeImmutable();
		$this->now100 = $this->now100->add(new \DateInterval('P100Y'));
		$this->now = new DateTimeImmutable();
		$this->dbal->query('SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;');
		$this->importLibrary();
		$this->importImages();

		$this->importParameters();
//
		dump('importProducts');
		$this->importProducts();

//

		dump('importParameterProduct');
		$this->importParameterProduct();
//
//

		dump('importAlias');
		$this->importAlias();


		dump('importCategory');
		$this->importCategory();

		dump('importProductImages');
		$this->importProductImages();


	}

	private function importLibrary(): void
	{
		$this->dbal->query('TRUNCATE `library_tree`;');
		//$this->zbDbal->query("UPDATE `library_tree` set editedTime = '2022-10-20 00:00:00' where editedTime = '0000-00-00 00:00:00';");


		$zbLibs = $this->zbDbal->query('SELECT * FROM library_tree');
		foreach ($zbLibs as $zbLib) {
			// replace image in meziviny

			$this->dbal->query("REPLACE`library_tree` SET
				id = %?i,
				parentId = %?i,
				level = %?i,
				last = %?i,
				path = %?s,
				name = %?s,
				nameTitle = %?s,
				nameAnchor = %?s,
				uid = %?s,
				sort = %?i,
				created = %?i,
				edited = %?i,
				createdTime = %?dt,
				editedTime = %?dt,
				publicFrom = %?dt,
				publicTo = %?dt

				"
				, $zbLib->id
				, $zbLib->idParent
				, $zbLib->level
				, $zbLib->last
				, $zbLib->path
				, $zbLib->name
				, $zbLib->nameTitle
				, $zbLib->nameAnchor
				, $zbLib->uid
				, $zbLib->sort
				, $zbLib->created
				, $zbLib->edited
				, $this->fixDateTimeValue($zbLib->createdTime)
				, $this->fixDateTimeValue($zbLib->editedTime)
				, $this->fixDateTimeValue($zbLib->publicFrom, $this->now)
				, $this->fixDateTimeValue($zbLib->publicTo, $this->now100)
			);
		}
	}

	private function importImages()
	{

		$this->dbal->query('TRUNCATE `image`;');

		$zbImages = $this->zbDbal->query('SELECT * FROM image');
		foreach ($zbImages as $zbImage) {
			// replace image in meziviny
			$this->dbal->query("REPLACE`image` SET
				id = %?i
				, name=%s
				, filename=%s
				, sourceImage=%s
				, libraryId=%?i
				, sort=%?i
				, md5=''
				"


				, $zbImage->id
				, $zbImage->name
				, $zbImage->filename
				, $zbImage->filename
				, $zbImage->cat
				, $zbImage->sort
			);
		}
	}

	private function importProducts()
	{
		$this->dbal->query('TRUNCATE `product_file`;');
		$this->dbal->query('TRUNCATE `product_localization`;');
		$this->dbal->query('TRUNCATE `product_product`;');

		$this->dbal->query('TRUNCATE `product_variant`;');
		$this->dbal->query('TRUNCATE `product_variant_localization`;');
		$this->dbal->query('TRUNCATE `product`;');
		$this->dbal->query('TRUNCATE `product_variant_price`;');
		$this->dbal->query('TRUNCATE `stock_supplies`;');

		foreach ($this->zbDbal->query('SELECT * FROM product') as $product) {
			$this->createProduct($product);

		}

	}

	private function createProduct(\Nextras\Dbal\Result\Row $product)
	{

		$data = [
			'id = %?i' => $product->id,
			'isSet = %?i' => $product->isSet,
			'isMikrosvin = %?i' => $product->isMikrosvin,
			'isAction = %?i' => $product->isAction,
			'isVoucher = %?i' => 0,
			'isNew = %?i' => $product->isNew,
			'isSale = %?i' => $product->isBargain,
			'isBestSeller = %?i' => $product->isBestseller,
			'storeLimit = %?i' => $product->storeLimit,
			'hideFirstImage = %?i' => $product->hideFirstImage,
			'template = %?s' => $product->template,
			'publicFrom = %?dt' => $this->fixDateTimeValue($product->publicFrom, $this->now),
			'publicTo = %?dt' => $this->fixDateTimeValue($product->publicTo, $this->now100),
			'internalName = %s' => $product->name,
			'customFieldsJson = %s' => '{}',
			'availability = %s' => '',
			'editedTime = %dt' => $this->now,


		];
		$head = implode(', ', array_keys($data));
		$this->dbal->query("REPLACE`product` SET ". $head, ... array_values($data));

		$this->createVariant($product);
		$this->createLocalization($product);
	}

	private function fixDateTimeValue(DateTimeImmutable|null $dateTime, $nullValue = null)
	{

		if ($dateTime === null) {
			return $dateTime;
		}

		if ($dateTime->getTimestamp() < 0) {
			if ($nullValue !== null) {
				$dateTime = $nullValue;
			} else {
				$dateTime = null;
			}
		}
		return $dateTime;
	}

	private function createVariant($product)
	{

		$data = [
			'productId = %?i' => $product->id,
			'param1ValueId = %?i' => null,
			'param2ValueId = %?i' => null,
			'ean = %?s' => '',
			'code = %?s' => '',
			'created = %?dt' => $this->now ,
			'createdBy = %?i' => null ,
			'edited = %?dt' => $this->now,
			'editedBy = %?i' =>null ,
			'sort = %?i' => 1 ,
			'soldCount = %?i' => 0 ,
			'isInDiscount = %?i' => 0 ,
			'extId = %?i' => null ,
		];

		$head = implode(', ', array_keys($data));
		$this->dbal->query("REPLACE`product_variant` SET ". $head, ... array_values($data));


		$variantId = $this->dbal->getLastInsertedId();
		$this->createVarinatLocalization($variantId);
		$this->createStock($product, $variantId);
		$this->createPrice($product, $variantId);

	}

	private function createVarinatLocalization(int $variantId)
	{

		$data = [
			'variantId = %?i' => $variantId,
			'mutationId = %?i' => 1,
			'active = %?i' => 1,
			'name = %?s' => '',
		];

		$head = implode(', ', array_keys($data));
		$this->dbal->query("REPLACE `product_variant_localization` SET ". $head, ... array_values($data));
	}

	private function createLocalization($product)
	{
		$data = [

			'productId = %?i' => $product->id,
			'mutationId = %?i' => 1,
			'voucherId = %?i' => null,
			'public = %?i' => 1,
			'name = %?s' => $product->name,
			'nameTitle = %?s' => $product->name,
			'nameAnchor = %?s' => $product->name,
			'description = %?s' => $product->description,
			'keywords = %?s' => $product->keywords,
			'annotation = %?s' => $product->annotation,
			'content = %?s' => $product->content,
			'videos = %?s' => '',
			'links = %?s' => '',
			'setup = %?s' => '',
			'customFieldsJson = %?s' => '',
			'customContentJson = %?s' => '',
		];

		$head = implode(', ', array_keys($data));
		$this->dbal->query("REPLACE `product_localization` SET ". $head, ... array_values($data));
	}

	private function createStore(\Nextras\Dbal\Result\Row $product)
	{
		$data = [

			'productId = %?i' => $product->id,
			'mutationId = %?i' => 1,
			'voucherId = %?i' => null,
			'public = %?i' => 1,
			'name = %?s' => $product->name,
			'nameTitle = %?s' => $product->name,
			'nameAnchor = %?s' => $product->name,
			'description = %?s' => $product->description,
			'keywords = %?s' => $product->keywords,
			'annotation = %?s' => $product->annotation,
			'content = %?s' => $product->content,
			'videos = %?s' => '',
			'links = %?s' => '',
			'setup = %?s' => '',
			'customFieldsJson = %?s' => '',
			'customContentJson = %?s' => '',
		];

		$head = implode(', ', array_keys($data));
		$this->dbal->query("REPLACE `product_localization` SET ". $head, ... array_values($data));
	}

	private function createStock($product, mixed $variantId)
	{
		$data = [

			'stockId = %?i' => 1,
			'variantId = %?i' => $variantId,
			'amount = %?i' => ($product->amount)? $product->amount : 0,
			'lastImport = %?s' => null,
		];

		$head = implode(', ', array_keys($data));
		$this->dbal->query("REPLACE `stock_supplies` SET  ". $head, ... array_values($data));
	}

	private function createPrice($product, mixed $variantId)
	{


		$priceLevels = $this->getPriceLevels();

		foreach ($priceLevels as $priceLevel) {
			$data = [
				'mutationId =  %?i' => 1,
				'priceLevelId =  %?i' => $priceLevel->id,
				'productId =  %?i' => $product->id,
				'productVariantId =  %?i' => $variantId,
				'price =  %?i' => $this->getPrice($product, $priceLevel),
			];

			$head = implode(', ', array_keys($data));
			$this->dbal->query("REPLACE `product_variant_price` SET  ". $head, ... array_values($data));
		}


	}

	private function getPriceLevels()
	{
		if ($this->priceLevel === null) {
			$this->priceLevel = $this->orm->priceLevel->findAll()->fetchAll();
		}

		return $this->priceLevel;
	}

	private function getPrice($product, PriceLevel $priceLevel)
	{
		$priceName = ($priceLevel->cleanLevel === '0') ? 'priceDPH' : sprintf('price%dDPH', $priceLevel->cleanLevel);


		if ($priceLevel->cleanLevel === 'action') {
			return (int)$product->priceDiscountDPH;
		} else {
			if ($product->isMikrosvin && $priceLevel->cleanLevel == '10') {
				return (int)$product->priceDPH;
			} else {
				return (int)$product->$priceName;
			}
		}



	}

	private function importParameters()
	{
		foreach ($this->zbDbal->query('SELECT * FROM parameter') as $parameter) {
			$this->createParameter($parameter);
		}


		$this->importTextParameterValues();
	}

	private function createParameter(\Nextras\Dbal\Result\Row $parameter)
	{
		if ($newParameter = $this->getParameterByName($parameter->name)) {
		} else {

			$data = [

				'parentId = %?i' => 1,
				'name = %?s' => $parameter->name,
				'uid = %?s' => Strings::webalize($parameter->name),
				'type = %?s' => $parameter->type,
				'sort = %?i' => $parameter->sort,
				'variantParameter = %?i' => 0,
				'isInFilter = %?i' => 0,
				'category = %?s' => null,
				'customFieldsJson = %?s' => '{}',
				'isProtected = %?i' => 0,
				'extId = %?i' => null,
			];

			$head = implode(', ', array_keys($data));
			$this->dbal->query("REPLACE `parameter` SET  ". $head, ... array_values($data));

			$newParameter = $this->getParameterByName($parameter->name);
		}


		$this->createParameterValues($newParameter);
	}

	private function createParameterValues(?\Nextras\Dbal\Result\Row $newParameter)
	{
		$values = $this->zbDbal->query('
				select v.* from parameter_value as v
				left join parameter as p on (p.id = v.idparameter)
				where p.name = %s
			', $newParameter->name);

		foreach ($values as $value) {
			$this->createParameterValue($newParameter, $value);
		}
	}

	private function getParameterByName(string $name)
	{
		return $this->dbal->query('select * from parameter where name = %s', $name)->fetch();
	}

	private function getParameterValueByValue($newParameter, string $value)
	{

		return $this->dbal->query('select * from parameter_value where internalValue = %s and parameterId = %i', $value, $newParameter->id)->fetch();
	}

	private function createParameterValue(?\Nextras\Dbal\Result\Row $newParameter,  $value)
	{
		if ($newValue = $this->getParameterValueByValue($newParameter, $value->value)) {

		} else {
			$data = [
				'parameterId = %?i' => $newParameter->id,
				'internalValue = %?s' => $value->value,
				'internalAlias = %?s' => Strings::webalize($value->value),
				'sort = %?i' => $value->sort,
				'parameterSort = %?i' => $value->sort,
				'extId = %?i' => null,
				'nextVintagesOffer = %?i' => 0,
				'customFieldsJson = %?s' => '{}',
			];

			$head = implode(', ', array_keys($data));
			$this->dbal->query("REPLACE `parameter_value` SET  ". $head, ... array_values($data));

			$newValue = $this->getParameterValueByValue($newParameter, $value->value);
		}
	}

	private function importTextParameterValues()
	{
		$ppRows = $this->zbDbal->query("
				select pp.value as value, p.name as name from product_parameter as pp
				left join parameter as p on (pp.idparameter = p.id)
				where p.type in ('text', 'number', 'bool')
			");

		foreach ($ppRows as $ppRow) {

			$newParameter = $this->getParameterByName($ppRow->name);
			$newValue = new \stdClass();
			$newValue->sort = 0;
			$newValue->value = $ppRow->value;
			$this->createParameterValue($newParameter, $newValue);
		}
	}

	private function importParameterProduct()
	{
		$this->dbal->query('TRUNCATE `product_parameter`;');
		$this->dbal->query('TRUNCATE `product_parameter_icon`;');

		$ppRows = $this->zbDbal->query("
				select idtree as productId , pp.value as value, p.name as name, pv.value as pv_value, p.type as type
				from product_parameter as pp
				left join parameter as p on (pp.idparameter = p.id)
				left join parameter_value as pv on (pp.value = pv.id and pp.idparameter = pv.idparameter)

			");

		foreach ($ppRows as $ppRow) {

			$newParameterValues = [];
			if ($ppRow->name) {
				$newParameter = $this->getParameterByName($ppRow->name);

				if ($newParameter) {

					if ($ppRow->type === 'select') {

						if ($ppRow->pv_value) {
							$newParameterValues[] = $this->getParameterValueByValue($newParameter, $ppRow->pv_value);
						}

					}
					if (in_array($ppRow->type, ['text', 'number', 'bool'])) {

						if ($ppRow->value) {
							$newParameterValues[] = $this->getParameterValueByValue($newParameter, $ppRow->value);
						}
					}

					if (in_array($ppRow->type, ['multiselect'])) {
						if ($ppRow->value) {
							foreach (explode('|', $ppRow->value) as $value) {
								$bzParameterValue = $this->zbDbal->query('select value from parameter_value where id = %i', $value)->fetch();

								if ($bzParameterValue) {
									$newParameterValues[] = $this->getParameterValueByValue($newParameter, $bzParameterValue->value);
								}

							}
						}
					}


					if (isset($newParameterValues) && $newParameterValues) {

						foreach ($newParameterValues as $newParameterValue) {
//							dump($newParameterValue);
							$data = [
								'productId = %i' => $ppRow->productId,
								'parameterId = %i' => $newParameter->id,
								'parameterValueId = %i' => $newParameterValue->id,
							];

							$head = implode(', ', array_keys($data));
							$this->dbal->query("REPLACE `product_parameter` SET  ". $head, ... array_values($data));
						}
					} else {
//						dump($newParameter->id);
//						dump($ppRow);
					}
				}
			}

		}

	}

	private function importAlias()
	{
		$this->dbal->query("delete from alias where module = 'productLocalization'");
		$res = $this->zbDbal->query("select * from alias where modul = 'product' and idref > 0 ");
		foreach ($res as $row) {

			$productLocalization = $this->dbal->query('select id from product_localization where productId = %i', $row->idref)->fetch();

			if ($productLocalization) {
				$data = [
					'alias = %s' => $row->alias,
					'module = %s' => 'productLocalization',
					'referenceId = %i' => $productLocalization->id,
					'mutationId = %i' => 1,
				];

				$head = implode(', ', array_keys($data));

				try {
					$this->dbal->query("insert `alias` SET  ". $head, ... array_values($data));
				} catch (\Exception $e) {

					$data['alias = %s'] = $data['alias = %s'] . '_' . uniqid();
					$this->dbal->query("insert `alias` SET  ". $head, ... array_values($data));
				}
			}


		}
	}

	private function importCategory()
	{

		$this->dbal->query('TRUNCATE `product_tree`;');

		$products = $this->dbal->query('select id from product');

		$mutation = $this->orm->mutation->getDefault();
		$this->orm->setMutation($mutation);

		foreach ($products as $product) {

			$entity = new ProductTree();
			$this->orm->productTree->attach($entity);

			$entity->product = $product->id;
			$entity->tree = 21;
			$entity->sort = 0;
			$this->orm->productTree->persistAndFlush($entity);
		}

	}

	private function importProductImages()
	{
		$this->dbal->query('TRUNCATE `product_image`;');

		$rows = $this->zbDbal->query('SELECT idtree AS productId, id AS imageId, i.name AS name, i.url AS url, i.sort AS sort from product_image AS i');
		foreach ($rows as $row) {

			$product = $this->dbal->query('select * from  product where id =%i', $row->productId)->fetch();
			$image = $this->dbal->query('select * from  image where id =%i', $row->imageId)->fetch();

			if ($product && $image) {
				$data = [
					'productId = %i' => $row->productId,
					'imageId = %i' => $row->imageId,
					'name = %s' => $row->name,
					'url = %s' => $row->url,
					'sort = %i' => $row->sort,
				];

				$head = implode(', ', array_keys($data));
				$this->dbal->query("insert `product_image` SET  ". $head, ... array_values($data));
			}
		}


	}

}

