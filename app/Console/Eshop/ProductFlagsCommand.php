<?php declare(strict_types = 1);

namespace App\Console\Eshop;

use App\Model\Orm;
use App\Model\OrmCleaner;
use App\Model\ProductModel;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * This command is just a template. It should be adjusted as needed on real projects
 * .
 */
class ProductFlagsCommand extends Command
{

	public function __construct(
		private readonly ProductModel $productModel,
		private readonly Orm $orm,
		private readonly OrmCleaner $ormCleaner,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->setName('Eshop:product:flags')
			->setDescription('Recalculate flags and maybe more...');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->orm->product->setPublicOnly(false);
		$this->orm->productLocalization->setPublicOnly(false);

		$this->updateFlags($output);

		$output->writeln('Done');
		return self::SUCCESS;
	}

	/**
	 * @todo: update ES
	 */
	protected function updateFlags(OutputInterface $output): void
	{
		$lastId = 0;
		$cond = []; //@todo set an appropriate condition
		$productsCount = $this->orm->product->findBy($cond)->countStored();

		$output->writeln('*** Product flags ***');
		$progressBar = new ProgressBar($output, $productsCount);
		$progressBar->start();
		$updatedCount = 0;

		do {
			$products = $this->orm->product
				->findBy($cond + ['id>' => $lastId])->orderBy('id')
				->limitBy(200)
				->fetchAll();

			foreach ($products as $product) {
				if ($this->productModel->handleFlags($product)) {
					$this->orm->persistAndFlush($product);
					$updatedCount++;
				}

				$lastId = $product->id;
				$progressBar->advance();
			}

			// release the entities from the memory
			$this->ormCleaner->safeClear();
		} while ($products !== []);

		$progressBar->finish();
		$output->writeln('');
		$output->writeln('Updated products: ' . $updatedCount);
		$output->writeln('');
	}

}
