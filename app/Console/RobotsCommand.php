<?php declare(strict_types = 1);

namespace App\Console;

use Exception;
use SuperKoderi\Console\Model\Robots;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class RobotsCommand extends Command
{

	use LockableTrait;

	private Robots $robots;

	public function __construct(
		Robots $robots
	)
	{
		parent::__construct(null);
		$this->robots = $robots;
	}


	protected static $defaultName = 'robots';

	protected function configure(): void
	{
//		$this->setName('foo');
		$this->setDescription('write to robots.txt file');
	}

	/**
	 * @throws Exception
	 */
	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		if (!$this->lock()) {
			throw new Exception('The command is already running in another process.');
		}

		$this->runCommand();
		$output->writeln('DONE');
		return 0;
	}

	private function runCommand(): void
	{
		ini_set('memory_limit', '500M');
		$this->robots->write();
	}

}
